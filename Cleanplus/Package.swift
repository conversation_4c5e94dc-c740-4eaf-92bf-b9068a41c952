// swift-tools-version: 5.9
import PackageDescription

let package = Package(
    name: "PhotoCleanerApp",
    platforms: [
        .iOS(.v17)
    ],
    products: [
        .library(
            name: "PhotoCleanerApp",
            targets: ["PhotoCleanerApp"]
        ),
    ],
    dependencies: [
        // Add any external dependencies here
    ],
    targets: [
        .target(
            name: "PhotoCleanerApp",
            dependencies: [],
            path: "PhotoCleanerApp"
        ),
    ]
)
