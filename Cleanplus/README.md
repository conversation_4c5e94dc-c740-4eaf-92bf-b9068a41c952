# Photo Cleaner App

A powerful iOS app that helps users clean up their photo library by identifying and removing duplicate, blurry, and unwanted photos. Inspired by popular apps like "Cleanup: Phone Storage Cleaner" and "Cleaner Guru".

## Features

### 🔍 Smart Photo Detection
- **Duplicate Detection**: Identifies exact duplicates and visually similar photos using advanced hashing algorithms
- **Blur Detection**: Uses Laplacian variance and Core Image filters to detect out-of-focus or blurry images
- **Closed Eyes Detection**: Employs Vision framework to identify photos where people have their eyes closed
- **Quality Assessment**: Evaluates overall photo quality and suggests low-quality images for removal

### 📱 Intuitive User Interface
- **Swipe Interface**: Tinder-like swipe gestures (left to delete, right to keep)
- **Native iOS Design**: Clean, modern interface following iOS design guidelines
- **Progress Tracking**: Real-time progress indicators during scanning and review
- **Confirmation Dialogs**: Safe deletion with review and confirmation steps

### 🛡️ Privacy & Security
- **Offline Processing**: All analysis happens locally on device - no internet required
- **Secure Permissions**: Proper iOS photo library permissions with clear explanations
- **Safe Deletion**: Trash/review system before permanent deletion

### 📊 Smart Analytics
- **Statistics Dashboard**: Shows counts of duplicates, blurry photos, and closed-eye photos
- **Confidence Scoring**: Each detection includes a confidence score for user decision-making
- **Batch Processing**: Efficient processing of large photo libraries

## Technical Architecture

### Core Components

1. **PhotoLibraryManager**: Manages photo library access and coordinates scanning operations
2. **Detection Algorithms**:
   - `DuplicateDetector`: SHA256 hashing and perceptual hashing for duplicate detection
   - `BlurDetector`: Laplacian variance calculation for blur detection
   - `EyeDetector`: Vision framework integration for facial landmark analysis
3. **SwipeView**: Main user interface for photo review with gesture handling
4. **ContentView**: Home screen with scanning controls and statistics

### Key Technologies
- **SwiftUI**: Modern declarative UI framework
- **Photos Framework**: iOS photo library access and management
- **Vision Framework**: Face detection and landmark analysis
- **Core Image**: Advanced image processing and filtering
- **CryptoKit**: Secure hashing for duplicate detection

## Project Structure

```
PhotoCleanerApp/
├── PhotoCleanerApp.swift          # Main app entry point
├── ContentView.swift              # Home screen and scanning interface
├── Models/
│   └── PhotoLibraryManager.swift  # Photo library management and data models
├── Views/
│   └── SwipeView.swift            # Main photo review interface
├── Detectors/
│   ├── DuplicateDetector.swift    # Duplicate photo detection
│   ├── BlurDetector.swift         # Blur detection algorithm
│   └── EyeDetector.swift          # Closed eyes detection
└── Assets.xcassets/               # App icons and assets
```

## Getting Started

### Prerequisites
- Xcode 15.0 or later
- iOS 17.0 or later
- macOS development environment

### Installation & Setup

Since Xcode project files can be complex to generate programmatically, here's how to set up the project:

#### Method 1: Create New Xcode Project (Recommended)
1. **Open Xcode** and create a new project
2. Choose **iOS** → **App**
3. Set the following:
   - Product Name: `PhotoCleanerApp`
   - Interface: `SwiftUI`
   - Language: `Swift`
   - Minimum Deployment: `iOS 17.0`
4. **Replace the generated files** with the ones from the `PhotoCleanerApp/` folder
5. **Add required frameworks** in Build Phases → Link Binary With Libraries:
   - `Photos.framework`
   - `Vision.framework`
   - `CoreImage.framework`
   - `CryptoKit.framework`
6. **Set photo library permission** in Info.plist:
   - Add key: `NSPhotoLibraryUsageDescription`
   - Value: `"This app needs access to your photo library to help you clean up duplicate, blurry, and unwanted photos."`

#### Method 2: Manual File Setup
1. Copy all Swift files from `PhotoCleanerApp/` to your new Xcode project
2. Ensure the folder structure matches:
   ```
   PhotoCleanerApp/
   ├── PhotoCleanerApp.swift
   ├── ContentView.swift
   ├── Models/PhotoLibraryManager.swift
   ├── Views/SwipeView.swift
   └── Detectors/
       ├── DuplicateDetector.swift
       ├── BlurDetector.swift
       └── EyeDetector.swift
   ```
3. Add the asset catalogs and preview content

### Build Configuration
- **Deployment Target**: iOS 17.0
- **Swift Version**: 5.0
- **Bundle Identifier**: `com.cleanplus.PhotoCleanerApp`

### Permissions
The app requires photo library access to function. Users will be prompted to grant permission on first launch.

## How It Works

### 1. Photo Library Scanning
- Fetches all photos from user's library using Photos framework
- Processes photos in batches to optimize memory usage
- Generates multiple types of analysis for each photo

### 2. Detection Algorithms

#### Duplicate Detection
- **Exact Duplicates**: SHA256 hash comparison for identical files
- **Similar Photos**: Perceptual hashing with Hamming distance calculation
- **Grouping**: Groups similar photos and suggests keeping the best quality

#### Blur Detection
- **Laplacian Variance**: Calculates image sharpness using edge detection
- **Threshold-based**: Configurable blur threshold for sensitivity adjustment
- **Core Image Integration**: Alternative method using Gaussian blur comparison

#### Closed Eyes Detection
- **Face Detection**: Uses Vision framework to locate faces in photos
- **Landmark Analysis**: Analyzes eye landmarks to determine open/closed state
- **Eye Aspect Ratio**: Calculates geometric ratios to assess eye closure

### 3. User Review Process
- **Card-based Interface**: Photos presented as swipeable cards
- **Gesture Controls**: Swipe left to delete, right to keep
- **Confidence Display**: Shows detection confidence for user decision-making
- **Batch Deletion**: Review all selections before permanent deletion

## Customization

### Detection Sensitivity
Adjust detection thresholds in the respective detector classes:
- `BlurDetector.blurThreshold`: Blur sensitivity (default: 100.0)
- `DuplicateDetector.hammingDistance`: Similarity threshold (default: 5)
- `EyeDetector.eyeAspectRatio`: Eye closure threshold (default: 0.23)

### UI Customization
- Modify colors and styling in SwiftUI views
- Adjust swipe thresholds in `SwipeView.swipeThreshold`
- Customize card animations and transitions

## Performance Considerations

- **Memory Management**: Processes photos in batches to avoid memory issues
- **Background Processing**: Heavy computations run on background queues
- **Image Resizing**: Uses smaller image sizes for analysis to improve speed
- **Async/Await**: Modern concurrency for responsive UI

## Future Enhancements

- [ ] Video size analysis and cleanup
- [ ] Screenshot detection and organization
- [ ] Live Photo management
- [ ] Cloud storage integration
- [ ] Advanced AI-based quality assessment
- [ ] Custom deletion rules and filters

## Contributing

This project serves as a foundation for a photo cleaning app. Feel free to extend and customize based on your specific needs.

## License

This project is created for educational and development purposes. Please ensure compliance with App Store guidelines and privacy regulations when distributing.
