# PhotoCleanerApp Setup Instructions

## 🚨 Project Setup Issue Resolved

The Xcode project file was corrupted during generation. Here's how to set up the project correctly:

## ✅ Quick Setup (5 minutes)

### Step 1: Create New Xcode Project
1. Open **Xcode**
2. Click **"Create a new Xcode project"**
3. Choose **iOS** → **App**
4. Fill in project details:
   - **Product Name**: `PhotoCleanerApp`
   - **Team**: Select your development team
   - **Organization Identifier**: `com.cleanplus.PhotoCleanerApp`
   - **Bundle Identifier**: `com.cleanplus.PhotoCleanerApp`
   - **Language**: `Swift`
   - **Interface**: `SwiftUI`
   - **Use Core Data**: ❌ (unchecked)
   - **Include Tests**: ✅ (optional)

### Step 2: Replace Generated Files
1. **Delete** the generated `ContentView.swift` and `PhotoCleanerAppApp.swift`
2. **Copy** all files from the `PhotoCleanerApp/` folder into your Xcode project
3. **Maintain the folder structure**:
   ```
   PhotoCleanerApp/
   ├── PhotoCleanerApp.swift          ← Main app file
   ├── ContentView.swift              ← Home screen
   ├── Models/
   │   └── PhotoLibraryManager.swift  ← Core data management
   ├── Views/
   │   └── SwipeView.swift            ← Swipe interface
   ├── Detectors/
   │   ├── DuplicateDetector.swift    ← Duplicate detection
   │   ├── BlurDetector.swift         ← Blur detection
   │   └── EyeDetector.swift          ← Eye detection
   └── Assets.xcassets/               ← App icons
   ```

### Step 3: Configure Project Settings
1. **Select your project** in the navigator
2. **Select the target** (PhotoCleanerApp)
3. **General tab**:
   - **Deployment Info** → **iOS Deployment Target**: `17.0`
   - **Supported Destinations**: iPhone, iPad

### Step 4: Add Required Frameworks
1. **Build Phases** tab
2. **Link Binary With Libraries** section
3. Click **"+"** and add:
   - `Photos.framework`
   - `Vision.framework`
   - `CoreImage.framework`
   - `CryptoKit.framework`
   - `Accelerate.framework`

### Step 5: Add Privacy Permission
1. **Info** tab (or Info.plist)
2. **Custom iOS Target Properties**
3. Add new entry:
   - **Key**: `Privacy - Photo Library Usage Description` (or `NSPhotoLibraryUsageDescription`)
   - **Value**: `This app needs access to your photo library to help you clean up duplicate, blurry, and unwanted photos.`

### Step 6: Build and Run
1. **Select a simulator** or connected device
2. **Press Cmd+R** or click the **Play** button
3. **Grant photo library permission** when prompted

## 🎯 Expected Result

After setup, you should have a fully functional photo cleaner app with:
- ✅ Photo library scanning
- ✅ Duplicate detection
- ✅ Blur detection  
- ✅ Closed eyes detection
- ✅ Swipe interface for review
- ✅ Safe deletion system

## 🔧 Troubleshooting

### Build Errors
- **"Cannot find 'PHPhotoLibrary' in scope"**: Add Photos.framework
- **"Cannot find 'VNDetectFaceLandmarksRequest' in scope"**: Add Vision.framework
- **Permission denied**: Add NSPhotoLibraryUsageDescription to Info.plist

### Runtime Issues
- **No photos detected**: Grant photo library permission in Settings → Privacy
- **App crashes on scan**: Check iOS deployment target is 17.0+
- **Slow performance**: Test on device rather than simulator

## 📱 Testing the App

1. **Launch the app**
2. **Tap "Grant Permission"** when prompted
3. **Tap "Start Scanning"** to analyze photos
4. **Wait for scanning** to complete (progress shown)
5. **Swipe through detected issues**:
   - **Swipe LEFT** → Delete photo
   - **Swipe RIGHT** → Keep photo
6. **Review and confirm** deletions

## 🚀 Next Steps

Once the basic app is working, you can:
- Customize detection sensitivity in detector classes
- Modify UI colors and styling in SwiftUI views
- Add video analysis features
- Implement additional photo quality metrics
- Add batch deletion capabilities

## 📞 Need Help?

If you encounter any issues:
1. Check the console for error messages
2. Verify all frameworks are properly linked
3. Ensure iOS deployment target is 17.0+
4. Test on a physical device with photos in the library

The app is designed to work with real photo libraries, so make sure you have some photos (including duplicates or blurry ones) for testing!
