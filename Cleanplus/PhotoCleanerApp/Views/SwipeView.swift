import SwiftUI
import Photos

struct SwipeView: View {
    @ObservedObject var photoLibraryManager: PhotoLibraryManager
    @State private var currentIndex = 0
    @State private var dragOffset = CGSize.zero
    @State private var showingDeleteConfirmation = false
    @State private var showingCompletionView = false
    @Environment(\.presentationMode) var presentationMode
    
    private let swipeThreshold: CGFloat = 100
    
    var body: some View {
        ZStack {
            Color.black.ignoresSafeArea()
            
            if currentIndex < photoLibraryManager.photosToReview.count {
                photoCardView
            } else {
                completionView
            }
            
            VStack {
                topBar
                Spacer()
                bottomControls
            }
        }
        .navigationBarHidden(true)
        .alert("Delete Selected Photos?", isPresented: $showingDeleteConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                Task {
                    await photoLibraryManager.deleteMarkedPhotos()
                    showingCompletionView = true
                }
            }
        } message: {
            Text("This will permanently delete \(photoLibraryManager.getMarkedPhotosCount()) photos from your library.")
        }
    }
    
    private var photoCardView: some View {
        ZStack {
            // Background cards for stack effect
            ForEach(max(0, currentIndex - 1)..<min(photoLibraryManager.photosToReview.count, currentIndex + 3), id: \.self) { index in
                if index < photoLibraryManager.photosToReview.count {
                    PhotoCard(
                        photo: photoLibraryManager.photosToReview[index],
                        offset: index == currentIndex ? dragOffset : .zero,
                        scale: index == currentIndex ? 1.0 : 0.95 - CGFloat(index - currentIndex) * 0.05,
                        opacity: index == currentIndex ? 1.0 : 0.5
                    )
                    .zIndex(Double(photoLibraryManager.photosToReview.count - index))
                }
            }
        }
        .gesture(
            DragGesture()
                .onChanged { value in
                    dragOffset = value.translation
                }
                .onEnded { value in
                    handleSwipeGesture(translation: value.translation)
                }
        )
    }
    
    private var topBar: some View {
        HStack {
            Button("Back") {
                presentationMode.wrappedValue.dismiss()
            }
            .foregroundColor(.white)
            
            Spacer()
            
            VStack(spacing: 4) {
                Text("\(currentIndex + 1) of \(photoLibraryManager.photosToReview.count)")
                    .foregroundColor(.white)
                    .font(.headline)
                
                ProgressView(value: Double(currentIndex), total: Double(photoLibraryManager.photosToReview.count))
                    .progressViewStyle(LinearProgressViewStyle(tint: .white))
                    .frame(width: 150)
            }
            
            Spacer()
            
            Button("Finish") {
                if photoLibraryManager.getMarkedPhotosCount() > 0 {
                    showingDeleteConfirmation = true
                } else {
                    showingCompletionView = true
                }
            }
            .foregroundColor(.white)
        }
        .padding()
    }
    
    private var bottomControls: some View {
        HStack(spacing: 40) {
            // Keep button
            Button(action: {
                keepCurrentPhoto()
            }) {
                VStack(spacing: 8) {
                    Image(systemName: "heart.fill")
                        .font(.system(size: 30))
                        .foregroundColor(.green)
                    
                    Text("Keep")
                        .foregroundColor(.white)
                        .font(.caption)
                }
            }
            .scaleEffect(dragOffset.width > swipeThreshold ? 1.2 : 1.0)
            .animation(.spring(response: 0.3), value: dragOffset)
            
            // Delete button
            Button(action: {
                deleteCurrentPhoto()
            }) {
                VStack(spacing: 8) {
                    Image(systemName: "trash.fill")
                        .font(.system(size: 30))
                        .foregroundColor(.red)
                    
                    Text("Delete")
                        .foregroundColor(.white)
                        .font(.caption)
                }
            }
            .scaleEffect(dragOffset.width < -swipeThreshold ? 1.2 : 1.0)
            .animation(.spring(response: 0.3), value: dragOffset)
        }
        .padding(.bottom, 50)
    }
    
    private var completionView: some View {
        VStack(spacing: 20) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(.green)
            
            Text("All Done!")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Text("You've reviewed all photos")
                .foregroundColor(.white.opacity(0.8))
            
            if photoLibraryManager.getMarkedPhotosCount() > 0 {
                Text("\(photoLibraryManager.getMarkedPhotosCount()) photos marked for deletion")
                    .foregroundColor(.red)
                    .padding(.top)
                
                Button("Delete Marked Photos") {
                    showingDeleteConfirmation = true
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.large)
            }
            
            Button("Done") {
                presentationMode.wrappedValue.dismiss()
            }
            .buttonStyle(.bordered)
            .controlSize(.large)
            .padding(.top)
        }
    }
    
    private func handleSwipeGesture(translation: CGSize) {
        if translation.width > swipeThreshold {
            // Swipe right - keep photo
            keepCurrentPhoto()
        } else if translation.width < -swipeThreshold {
            // Swipe left - delete photo
            deleteCurrentPhoto()
        } else {
            // Return to center
            withAnimation(.spring()) {
                dragOffset = .zero
            }
        }
    }
    
    private func keepCurrentPhoto() {
        guard currentIndex < photoLibraryManager.photosToReview.count else { return }
        
        let photo = photoLibraryManager.photosToReview[currentIndex]
        photoLibraryManager.unmarkPhotoForDeletion(photo)
        
        withAnimation(.spring()) {
            dragOffset = CGSize(width: 500, height: 0)
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            nextPhoto()
        }
    }
    
    private func deleteCurrentPhoto() {
        guard currentIndex < photoLibraryManager.photosToReview.count else { return }
        
        let photo = photoLibraryManager.photosToReview[currentIndex]
        photoLibraryManager.markPhotoForDeletion(photo)
        
        withAnimation(.spring()) {
            dragOffset = CGSize(width: -500, height: 0)
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            nextPhoto()
        }
    }
    
    private func nextPhoto() {
        currentIndex += 1
        dragOffset = .zero
    }
}

struct PhotoCard: View {
    let photo: PhotoToReview
    let offset: CGSize
    let scale: CGFloat
    let opacity: Double
    
    @State private var image: UIImage?
    
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.white)
                .shadow(radius: 10)
            
            VStack(spacing: 0) {
                // Photo
                if let image = image {
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(height: 400)
                        .clipped()
                        .cornerRadius(20, corners: [.topLeft, .topRight])
                } else {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(height: 400)
                        .cornerRadius(20, corners: [.topLeft, .topRight])
                        .overlay(
                            ProgressView()
                        )
                }
                
                // Info section
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: photo.reason.icon)
                            .foregroundColor(Color(photo.reason.color))
                        
                        Text(photo.reason.rawValue)
                            .font(.headline)
                            .foregroundColor(Color(photo.reason.color))
                        
                        Spacer()
                        
                        Text("\(Int(photo.confidence * 100))%")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Text(getReasonDescription(for: photo.reason))
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }
                .padding()
            }
        }
        .frame(width: 300, height: 500)
        .scaleEffect(scale)
        .opacity(opacity)
        .offset(offset)
        .rotationEffect(.degrees(Double(offset.width / 10)))
        .onAppear {
            loadImage()
        }
    }
    
    private func loadImage() {
        let options = PHImageRequestOptions()
        options.deliveryMode = .highQualityFormat
        options.resizeMode = .exact
        
        PHImageManager.default().requestImage(
            for: photo.asset,
            targetSize: CGSize(width: 300, height: 400),
            contentMode: .aspectFill,
            options: options
        ) { loadedImage, _ in
            DispatchQueue.main.async {
                self.image = loadedImage
            }
        }
    }
    
    private func getReasonDescription(for reason: ReviewReason) -> String {
        switch reason {
        case .duplicate:
            return "This photo appears to be a duplicate of another image in your library."
        case .blur:
            return "This photo appears to be blurry or out of focus."
        case .closedEyes:
            return "One or more people in this photo have their eyes closed."
        case .lowQuality:
            return "This photo appears to be of low quality."
        }
    }
}

extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

#Preview {
    SwipeView(photoLibraryManager: PhotoLibraryManager())
}
