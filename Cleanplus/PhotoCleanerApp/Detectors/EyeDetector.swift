import Foundation
import UIKit
import Vision

class EyeDetector {
    
    func hasClosedEyes(image: UIImage) async -> Bool {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                guard let cgImage = image.cgImage else {
                    continuation.resume(returning: false)
                    return
                }
                
                let request = VNDetectFaceRectanglesRequest { request, error in
                    guard error == nil,
                          let results = request.results as? [VNFaceObservation],
                          !results.isEmpty else {
                        continuation.resume(returning: false)
                        return
                    }
                    
                    // If we detect faces, check for closed eyes
                    self.checkForClosedEyes(in: cgImage, faces: results) { hasClosedEyes in
                        continuation.resume(returning: hasClosedEyes)
                    }
                }
                
                let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
                
                do {
                    try handler.perform([request])
                } catch {
                    continuation.resume(returning: false)
                }
            }
        }
    }
    
    func getClosedEyesConfidence(image: UIImage) async -> Float {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                guard let cgImage = image.cgImage else {
                    continuation.resume(returning: 0.0)
                    return
                }
                
                let request = VNDetectFaceLandmarksRequest { request, error in
                    guard error == nil,
                          let results = request.results as? [VNFaceObservation] else {
                        continuation.resume(returning: 0.0)
                        return
                    }
                    
                    var totalConfidence: Float = 0.0
                    var faceCount = 0
                    
                    for face in results {
                        if let landmarks = face.landmarks {
                            let eyeConfidence = self.calculateEyeClosureConfidence(landmarks: landmarks)
                            totalConfidence += eyeConfidence
                            faceCount += 1
                        }
                    }
                    
                    let averageConfidence = faceCount > 0 ? totalConfidence / Float(faceCount) : 0.0
                    continuation.resume(returning: averageConfidence)
                }
                
                let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
                
                do {
                    try handler.perform([request])
                } catch {
                    continuation.resume(returning: 0.0)
                }
            }
        }
    }
    
    private func checkForClosedEyes(in cgImage: CGImage, faces: [VNFaceObservation], completion: @escaping (Bool) -> Void) {
        let request = VNDetectFaceLandmarksRequest { request, error in
            guard error == nil,
                  let results = request.results as? [VNFaceObservation] else {
                completion(false)
                return
            }
            
            var hasClosedEyes = false
            
            for face in results {
                if let landmarks = face.landmarks {
                    if self.areEyesClosed(landmarks: landmarks) {
                        hasClosedEyes = true
                        break
                    }
                }
            }
            
            completion(hasClosedEyes)
        }
        
        let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
        
        do {
            try handler.perform([request])
        } catch {
            completion(false)
        }
    }
    
    private func areEyesClosed(landmarks: VNFaceLandmarks2D) -> Bool {
        // Check left eye
        if let leftEye = landmarks.leftEye {
            if isEyeClosed(eyePoints: leftEye.normalizedPoints) {
                return true
            }
        }
        
        // Check right eye
        if let rightEye = landmarks.rightEye {
            if isEyeClosed(eyePoints: rightEye.normalizedPoints) {
                return true
            }
        }
        
        return false
    }
    
    private func calculateEyeClosureConfidence(landmarks: VNFaceLandmarks2D) -> Float {
        var confidence: Float = 0.0
        var eyeCount = 0
        
        // Check left eye
        if let leftEye = landmarks.leftEye {
            confidence += getEyeClosureScore(eyePoints: leftEye.normalizedPoints)
            eyeCount += 1
        }
        
        // Check right eye
        if let rightEye = landmarks.rightEye {
            confidence += getEyeClosureScore(eyePoints: rightEye.normalizedPoints)
            eyeCount += 1
        }
        
        return eyeCount > 0 ? confidence / Float(eyeCount) : 0.0
    }
    
    private func isEyeClosed(eyePoints: [CGPoint]) -> Bool {
        guard eyePoints.count >= 6 else { return false }
        
        // Calculate eye aspect ratio (EAR)
        let ear = calculateEyeAspectRatio(eyePoints: eyePoints)
        
        // Threshold for closed eye (typically around 0.2-0.25)
        return ear < 0.23
    }
    
    private func getEyeClosureScore(eyePoints: [CGPoint]) -> Float {
        guard eyePoints.count >= 6 else { return 0.0 }
        
        let ear = calculateEyeAspectRatio(eyePoints: eyePoints)
        
        // Convert EAR to closure score (0.0 = open, 1.0 = closed)
        let closureScore = max(0.0, min(1.0, (0.3 - ear) / 0.3))
        return Float(closureScore)
    }
    
    private func calculateEyeAspectRatio(eyePoints: [CGPoint]) -> Double {
        guard eyePoints.count >= 6 else { return 1.0 }
        
        // Eye landmarks are typically arranged in a specific order
        // We'll use a simplified calculation based on the vertical and horizontal distances
        
        // Find the extreme points
        let minX = eyePoints.min(by: { $0.x < $1.x })?.x ?? 0
        let maxX = eyePoints.max(by: { $0.x < $1.x })?.x ?? 0
        let minY = eyePoints.min(by: { $0.y < $1.y })?.y ?? 0
        let maxY = eyePoints.max(by: { $0.y < $1.y })?.y ?? 0
        
        let width = Double(maxX - minX)
        let height = Double(maxY - minY)
        
        // Avoid division by zero
        guard width > 0 else { return 0.0 }
        
        // Eye aspect ratio
        let ear = height / width
        return ear
    }
    
    // Alternative method using simpler face detection
    func hasClosedEyesSimple(image: UIImage) async -> Bool {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                guard let cgImage = image.cgImage else {
                    continuation.resume(returning: false)
                    return
                }
                
                // Use CIDetector for face detection
                let context = CIContext()
                let ciImage = CIImage(cgImage: cgImage)
                
                let detector = CIDetector(
                    ofType: CIDetectorTypeFace,
                    context: context,
                    options: [
                        CIDetectorAccuracy: CIDetectorAccuracyHigh,
                        CIDetectorEyeBlink: true
                    ]
                )
                
                let features = detector?.features(in: ciImage) as? [CIFaceFeature] ?? []
                
                for feature in features {
                    if feature.leftEyeClosed || feature.rightEyeClosed {
                        continuation.resume(returning: true)
                        return
                    }
                }
                
                continuation.resume(returning: false)
            }
        }
    }
}
