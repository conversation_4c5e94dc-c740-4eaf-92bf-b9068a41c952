import Foundation
import Photos
import UIKit
import CryptoKit

class DuplicateDetector {
    private struct PhotoHash {
        let asset: PHAsset
        let hash: String
        let perceptualHash: String
    }
    
    func findDuplicates(in assets: [PHAsset], progressCallback: @escaping (Double) -> Void) async -> [[PHAsset]] {
        var photoHashes: [PhotoHash] = []
        let totalCount = assets.count
        
        // Generate hashes for all photos
        for (index, asset) in assets.enumerated() {
            if let image = await loadImage(from: asset) {
                let hash = generateImageHash(image)
                let perceptualHash = generatePerceptualHash(image)
                
                photoHashes.append(PhotoHash(
                    asset: asset,
                    hash: hash,
                    perceptualHash: perceptualHash
                ))
            }
            
            let progress = Double(index + 1) / Double(totalCount)
            progressCallback(progress)
        }
        
        // Group duplicates
        return groupDuplicates(photoHashes)
    }
    
    private func loadImage(from asset: PHAsset) async -> UIImage? {
        return await withCheckedContinuation { continuation in
            let options = PHImageRequestOptions()
            options.isSynchronous = false
            options.deliveryMode = .highQualityFormat
            options.resizeMode = .exact
            
            let targetSize = CGSize(width: 200, height: 200) // Small size for hashing
            
            PHImageManager.default().requestImage(
                for: asset,
                targetSize: targetSize,
                contentMode: .aspectFit,
                options: options
            ) { image, _ in
                continuation.resume(returning: image)
            }
        }
    }
    
    private func generateImageHash(_ image: UIImage) -> String {
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            return ""
        }
        
        let hash = SHA256.hash(data: imageData)
        return hash.compactMap { String(format: "%02x", $0) }.joined()
    }
    
    private func generatePerceptualHash(_ image: UIImage) -> String {
        // Simplified perceptual hashing algorithm
        // Resize to 8x8 grayscale and compare pixel intensities
        
        guard let resizedImage = resizeImage(image, to: CGSize(width: 8, height: 8)),
              let cgImage = resizedImage.cgImage else {
            return ""
        }
        
        let width = cgImage.width
        let height = cgImage.height
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8
        
        var pixelData = [UInt8](repeating: 0, count: width * height * bytesPerPixel)
        
        let context = CGContext(
            data: &pixelData,
            width: width,
            height: height,
            bitsPerComponent: bitsPerComponent,
            bytesPerRow: bytesPerRow,
            space: CGColorSpaceCreateDeviceRGB(),
            bitmapInfo: CGImageAlphaInfo.noneSkipLast.rawValue
        )
        
        context?.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
        
        // Convert to grayscale and calculate average
        var grayValues: [UInt8] = []
        for i in stride(from: 0, to: pixelData.count, by: bytesPerPixel) {
            let r = pixelData[i]
            let g = pixelData[i + 1]
            let b = pixelData[i + 2]
            let gray = UInt8(0.299 * Double(r) + 0.587 * Double(g) + 0.114 * Double(b))
            grayValues.append(gray)
        }
        
        let average = grayValues.reduce(0, +) / grayValues.count
        
        // Create hash based on whether each pixel is above or below average
        var hash = ""
        for gray in grayValues {
            hash += gray > average ? "1" : "0"
        }
        
        return hash
    }
    
    private func resizeImage(_ image: UIImage, to size: CGSize) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 1.0)
        defer { UIGraphicsEndImageContext() }
        
        image.draw(in: CGRect(origin: .zero, size: size))
        return UIGraphicsGetImageFromCurrentImageContext()
    }
    
    private func groupDuplicates(_ photoHashes: [PhotoHash]) -> [[PHAsset]] {
        var groups: [[PHAsset]] = []
        var processed: Set<String> = []
        
        for photoHash in photoHashes {
            if processed.contains(photoHash.hash) {
                continue
            }
            
            // Find exact duplicates first
            let exactDuplicates = photoHashes.filter { $0.hash == photoHash.hash }
            
            if exactDuplicates.count > 1 {
                groups.append(exactDuplicates.map { $0.asset })
                processed.insert(photoHash.hash)
                continue
            }
            
            // Find similar images using perceptual hash
            let similarImages = photoHashes.filter { other in
                other.hash != photoHash.hash &&
                !processed.contains(other.hash) &&
                hammingDistance(photoHash.perceptualHash, other.perceptualHash) <= 5 // Threshold for similarity
            }
            
            if !similarImages.isEmpty {
                var group = [photoHash.asset]
                group.append(contentsOf: similarImages.map { $0.asset })
                groups.append(group)
                
                processed.insert(photoHash.hash)
                for similar in similarImages {
                    processed.insert(similar.hash)
                }
            }
        }
        
        return groups
    }
    
    private func hammingDistance(_ hash1: String, _ hash2: String) -> Int {
        guard hash1.count == hash2.count else { return Int.max }
        
        var distance = 0
        for (char1, char2) in zip(hash1, hash2) {
            if char1 != char2 {
                distance += 1
            }
        }
        return distance
    }
}
