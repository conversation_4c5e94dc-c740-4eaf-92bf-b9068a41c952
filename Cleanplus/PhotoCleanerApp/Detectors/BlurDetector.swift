import Foundation
import UIKit
import CoreImage
import Accelerate

class BlurDetector {
    private let blurThreshold: Float = 100.0 // Threshold for determining blur
    
    func isBlurry(image: UIImage) async -> Bool {
        let variance = await calculateLaplacianVariance(image: image)
        return variance < blurThreshold
    }
    
    func getBlurConfidence(image: UIImage) async -> Float {
        let variance = await calculateLaplacianVariance(image: image)
        // Convert variance to confidence score (0.0 = not blurry, 1.0 = very blurry)
        let confidence = max(0.0, min(1.0, (blurThreshold - variance) / blurThreshold))
        return confidence
    }
    
    private func calculateLaplacianVariance(image: UIImage) async -> Float {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                guard let cgImage = image.cgImage else {
                    continuation.resume(returning: 0.0)
                    return
                }
                
                // Convert to grayscale
                guard let grayImage = self.convertToGrayscale(cgImage: cgImage) else {
                    continuation.resume(returning: 0.0)
                    return
                }
                
                // Apply Laplacian filter
                let variance = self.applyLaplacianFilter(to: grayImage)
                continuation.resume(returning: variance)
            }
        }
    }
    
    private func convertToGrayscale(cgImage: CGImage) -> CGImage? {
        let width = cgImage.width
        let height = cgImage.height
        
        let colorSpace = CGColorSpaceCreateDeviceGray()
        let context = CGContext(
            data: nil,
            width: width,
            height: height,
            bitsPerComponent: 8,
            bytesPerRow: width,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.none.rawValue
        )
        
        context?.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
        return context?.makeImage()
    }
    
    private func applyLaplacianFilter(to cgImage: CGImage) -> Float {
        let width = cgImage.width
        let height = cgImage.height
        
        guard let dataProvider = cgImage.dataProvider,
              let data = dataProvider.data,
              let bytes = CFDataGetBytePtr(data) else {
            return 0.0
        }
        
        // Laplacian kernel
        let kernel: [Float] = [
            0, -1, 0,
            -1, 4, -1,
            0, -1, 0
        ]
        
        var result: [Float] = []
        
        // Apply convolution
        for y in 1..<(height - 1) {
            for x in 1..<(width - 1) {
                var sum: Float = 0.0
                
                for ky in 0..<3 {
                    for kx in 0..<3 {
                        let pixelY = y - 1 + ky
                        let pixelX = x - 1 + kx
                        let pixelIndex = pixelY * width + pixelX
                        let pixelValue = Float(bytes[pixelIndex])
                        let kernelValue = kernel[ky * 3 + kx]
                        sum += pixelValue * kernelValue
                    }
                }
                
                result.append(abs(sum))
            }
        }
        
        // Calculate variance
        guard !result.isEmpty else { return 0.0 }
        
        let mean = result.reduce(0, +) / Float(result.count)
        let variance = result.map { pow($0 - mean, 2) }.reduce(0, +) / Float(result.count)
        
        return variance
    }
    
    // Alternative method using Core Image for better performance
    func isBlurryUsingCoreImage(image: UIImage) async -> Bool {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                guard let ciImage = CIImage(image: image) else {
                    continuation.resume(returning: false)
                    return
                }
                
                let context = CIContext()
                
                // Apply Gaussian blur and compare with original
                let blurFilter = CIFilter(name: "CIGaussianBlur")!
                blurFilter.setValue(ciImage, forKey: kCIInputImageKey)
                blurFilter.setValue(2.0, forKey: kCIInputRadiusKey)
                
                guard let blurredImage = blurFilter.outputImage else {
                    continuation.resume(returning: false)
                    return
                }
                
                // Calculate difference between original and blurred
                let differenceFilter = CIFilter(name: "CIDifferenceBlendMode")!
                differenceFilter.setValue(ciImage, forKey: kCIInputImageKey)
                differenceFilter.setValue(blurredImage, forKey: kCIInputBackgroundImageKey)
                
                guard let differenceImage = differenceFilter.outputImage else {
                    continuation.resume(returning: false)
                    return
                }
                
                // Calculate average intensity of difference
                let extent = differenceImage.extent
                let inputExtent = CIVector(cgRect: extent)
                
                let averageFilter = CIFilter(name: "CIAreaAverage")!
                averageFilter.setValue(differenceImage, forKey: kCIInputImageKey)
                averageFilter.setValue(inputExtent, forKey: kCIInputExtentKey)
                
                guard let averageImage = averageFilter.outputImage,
                      let cgImage = context.createCGImage(averageImage, from: averageImage.extent) else {
                    continuation.resume(returning: false)
                    return
                }
                
                // Extract pixel data
                let width = cgImage.width
                let height = cgImage.height
                let bytesPerPixel = 4
                let bytesPerRow = bytesPerPixel * width
                
                var pixelData = [UInt8](repeating: 0, count: width * height * bytesPerPixel)
                
                let colorSpace = CGColorSpaceCreateDeviceRGB()
                let bitmapContext = CGContext(
                    data: &pixelData,
                    width: width,
                    height: height,
                    bitsPerComponent: 8,
                    bytesPerRow: bytesPerRow,
                    space: colorSpace,
                    bitmapInfo: CGImageAlphaInfo.noneSkipLast.rawValue
                )
                
                bitmapContext?.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
                
                // Calculate average brightness
                let totalPixels = width * height
                var totalBrightness: Int = 0
                
                for i in stride(from: 0, to: pixelData.count, by: bytesPerPixel) {
                    let r = Int(pixelData[i])
                    let g = Int(pixelData[i + 1])
                    let b = Int(pixelData[i + 2])
                    totalBrightness += (r + g + b) / 3
                }
                
                let averageBrightness = Double(totalBrightness) / Double(totalPixels)
                
                // If the difference is small, the image is likely blurry
                let isBlurry = averageBrightness < 10.0 // Threshold for blur detection
                continuation.resume(returning: isBlurry)
            }
        }
    }
}
