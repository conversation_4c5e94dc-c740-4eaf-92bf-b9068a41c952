import SwiftUI
import Photos

struct ContentView: View {
    @StateObject private var photoLibraryManager = PhotoLibraryManager()
    @State private var showingPermissionAlert = false
    @State private var isScanning = false
    @State private var scanProgress: Double = 0.0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                VStack(spacing: 8) {
                    Image(systemName: "photo.on.rectangle.angled")
                        .font(.system(size: 60))
                        .foregroundColor(.blue)
                    
                    Text("Photo Cleaner")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("Clean up your photo library")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.top, 40)
                
                Spacer()
                
                // Main Content
                if photoLibraryManager.authorizationStatus == .authorized {
                    if isScanning {
                        scanningView
                    } else if photoLibraryManager.photosToReview.isEmpty {
                        startScanView
                    } else {
                        NavigationLink(destination: SwipeView(photoLibraryManager: photoLibraryManager)) {
                            reviewPhotosButton
                        }
                    }
                } else {
                    requestPermissionView
                }
                
                Spacer()
                
                // Stats
                if !photoLibraryManager.photosToReview.isEmpty {
                    statsView
                }
            }
            .padding()
            .navigationBarHidden(true)
        }
        .onAppear {
            photoLibraryManager.requestPhotoLibraryPermission()
        }
        .alert("Photo Library Access Required", isPresented: $showingPermissionAlert) {
            Button("Settings") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Please allow access to your photo library in Settings to use this app.")
        }
    }
    
    private var requestPermissionView: some View {
        VStack(spacing: 16) {
            Image(systemName: "photo.badge.plus")
                .font(.system(size: 50))
                .foregroundColor(.orange)
            
            Text("Access Your Photos")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("We need access to your photo library to help you identify and remove duplicate, blurry, and unwanted photos.")
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
            
            Button("Grant Access") {
                photoLibraryManager.requestPhotoLibraryPermission()
                if photoLibraryManager.authorizationStatus == .denied {
                    showingPermissionAlert = true
                }
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
        }
        .padding()
    }
    
    private var startScanView: some View {
        VStack(spacing: 16) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 50))
                .foregroundColor(.green)
            
            Text("Ready to Scan")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Tap below to start scanning your photo library for duplicates, blurry photos, and images with closed eyes.")
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
            
            Button("Start Scanning") {
                startScanning()
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
        }
        .padding()
    }
    
    private var scanningView: some View {
        VStack(spacing: 20) {
            ProgressView(value: scanProgress)
                .progressViewStyle(LinearProgressViewStyle())
                .scaleEffect(1.2)
            
            Text("Scanning Photos...")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("\(Int(scanProgress * 100))% Complete")
                .foregroundColor(.secondary)
            
            Text("Analyzing your photos for duplicates, blur, and closed eyes")
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
                .font(.caption)
        }
        .padding()
    }
    
    private var reviewPhotosButton: some View {
        VStack(spacing: 12) {
            Image(systemName: "hand.point.up.left")
                .font(.system(size: 40))
                .foregroundColor(.red)
            
            Text("Review \(photoLibraryManager.photosToReview.count) Photos")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Text("Swipe to keep or delete")
                .foregroundColor(.white.opacity(0.8))
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 20)
        .background(Color.red)
        .cornerRadius(12)
    }
    
    private var statsView: some View {
        HStack(spacing: 20) {
            StatCard(title: "Duplicates", count: photoLibraryManager.duplicateCount, color: .orange)
            StatCard(title: "Blurry", count: photoLibraryManager.blurryCount, color: .yellow)
            StatCard(title: "Closed Eyes", count: photoLibraryManager.closedEyesCount, color: .purple)
        }
        .padding(.bottom, 20)
    }
    
    private func startScanning() {
        isScanning = true
        scanProgress = 0.0
        
        Task {
            await photoLibraryManager.scanPhotoLibrary { progress in
                DispatchQueue.main.async {
                    scanProgress = progress
                }
            }
            
            DispatchQueue.main.async {
                isScanning = false
            }
        }
    }
}

struct StatCard: View {
    let title: String
    let count: Int
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text("\(count)")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(color.opacity(0.1))
        .cornerRadius(8)
    }
}

#Preview {
    ContentView()
}
