//
//  ContentView.swift
//  PhotoCleaner
//
//  Created by 王博 on 2025/7/26.
//

import SwiftUI
import Photos

struct ContentView: View {
    @StateObject private var photoLibraryManager = PhotoLibraryManager()
    @State private var showingPermissionAlert = false
    @State private var showingSwipeView = false

    var body: some View {
        NavigationView {
            ZStack {
                LinearGradient(
                    gradient: Gradient(colors: [Color.blue.opacity(0.6), Color.purple.opacity(0.6)]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()

                VStack(spacing: 30) {
                    // Header
                    VStack(spacing: 10) {
                        Image(systemName: "photo.on.rectangle.angled")
                            .font(.system(size: 80))
                            .foregroundColor(.white)

                        Text("Photo Cleaner")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        Text("Clean up duplicate, blurry, and unwanted photos")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.8))
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }

                    Spacer()

                    // Main content
                    if photoLibraryManager.authorizationStatus == .authorized {
                        authorizedView
                    } else {
                        permissionView
                    }

                    Spacer()
                }
                .padding()
            }
        }
        .onAppear {
            photoLibraryManager.authorizationStatus = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        }
        .alert("Photo Library Access Required", isPresented: $showingPermissionAlert) {
            Button("Settings") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Please enable photo library access in Settings to use this app.")
        }
        .fullScreenCover(isPresented: $showingSwipeView) {
            SwipeView(photoLibraryManager: photoLibraryManager)
        }
    }

    private var permissionView: some View {
        VStack(spacing: 20) {
            Image(systemName: "lock.shield")
                .font(.system(size: 60))
                .foregroundColor(.white.opacity(0.8))

            Text("Photo Library Access")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)

            Text("This app needs access to your photo library to help you clean up duplicate, blurry, and unwanted photos.")
                .font(.body)
                .foregroundColor(.white.opacity(0.9))
                .multilineTextAlignment(.center)
                .padding(.horizontal)

            Button("Grant Access") {
                photoLibraryManager.requestPhotoLibraryPermission()
            }
            .font(.headline)
            .foregroundColor(.blue)
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(Color.white)
            .cornerRadius(12)
            .padding(.horizontal)
        }
    }

    private var authorizedView: some View {
        VStack(spacing: 25) {
            if photoLibraryManager.isScanning {
                scanningView
            } else if photoLibraryManager.photosToReview.isEmpty {
                readyToScanView
            } else {
                resultsView
            }
        }
    }

    private var readyToScanView: some View {
        VStack(spacing: 20) {
            Image(systemName: "magnifyingglass.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.white.opacity(0.8))

            Text("Ready to Scan")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)

            Text("Tap the button below to start analyzing your photo library for duplicates, blurry photos, and other issues.")
                .font(.body)
                .foregroundColor(.white.opacity(0.9))
                .multilineTextAlignment(.center)
                .padding(.horizontal)

            Button("Start Scanning") {
                Task {
                    await photoLibraryManager.startScanning()
                }
            }
            .font(.headline)
            .foregroundColor(.blue)
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(Color.white)
            .cornerRadius(12)
            .padding(.horizontal)
        }
    }

    private var scanningView: some View {
        VStack(spacing: 20) {
            ProgressView(value: photoLibraryManager.scanProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: .white))
                .scaleEffect(1.2)

            Text("Scanning Photos...")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)

            Text(photoLibraryManager.scanStatusMessage)
                .font(.body)
                .foregroundColor(.white.opacity(0.9))
                .multilineTextAlignment(.center)
                .padding(.horizontal)

            // Statistics
            VStack(spacing: 8) {
                HStack {
                    Text("Total Photos:")
                    Spacer()
                    Text("\(photoLibraryManager.statistics.totalPhotos)")
                }
                .foregroundColor(.white.opacity(0.9))

                HStack {
                    Text("Issues Found:")
                    Spacer()
                    Text("\(photoLibraryManager.photosToReview.count)")
                }
                .foregroundColor(.white.opacity(0.9))
            }
            .font(.caption)
            .padding()
            .background(Color.white.opacity(0.1))
            .cornerRadius(8)
            .padding(.horizontal)
        }
    }

    private var resultsView: some View {
        VStack(spacing: 20) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.green)

            Text("Scan Complete!")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)

            Text("Found \(photoLibraryManager.photosToReview.count) photos that need your attention")
                .font(.body)
                .foregroundColor(.white.opacity(0.9))
                .multilineTextAlignment(.center)
                .padding(.horizontal)

            // Statistics grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 10) {
                StatCard(title: "Duplicates", count: photoLibraryManager.statistics.duplicates, icon: "doc.on.doc", color: .orange)
                StatCard(title: "Blurry", count: photoLibraryManager.statistics.blurryPhotos, icon: "eye.slash", color: .red)
                StatCard(title: "Closed Eyes", count: photoLibraryManager.statistics.closedEyePhotos, icon: "eye.slash.circle", color: .purple)
                StatCard(title: "Screenshots", count: photoLibraryManager.statistics.screenshots, icon: "camera.viewfinder", color: .blue)
                StatCard(title: "Large Videos", count: photoLibraryManager.statistics.largeVideos, icon: "video.badge.plus", color: .green)
                StatCard(title: "Live Photos", count: photoLibraryManager.statistics.livePhotos, icon: "livephoto", color: .cyan)
            }
            .padding(.horizontal)

            VStack(spacing: 12) {
                Button("Review Photos") {
                    showingSwipeView = true
                }
                .font(.headline)
                .foregroundColor(.blue)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(Color.white)
                .cornerRadius(12)
                .padding(.horizontal)

                Button("Scan Again") {
                    Task {
                        await photoLibraryManager.startScanning()
                    }
                }
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.8))
                .padding(.horizontal)
            }
        }
    }
}

struct StatCard: View {
    let title: String
    let count: Int
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text("\(count)")
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(.white)

            Text(title)
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
        }
        .frame(height: 80)
        .frame(maxWidth: .infinity)
        .background(Color.white.opacity(0.1))
        .cornerRadius(12)
    }
}

#Preview {
    ContentView()
}
