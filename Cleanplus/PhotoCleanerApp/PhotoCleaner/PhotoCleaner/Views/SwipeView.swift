//
//  SwipeView.swift
//  PhotoCleaner
//
//  Created by 王博 on 2025/7/26.
//

import SwiftUI
import Photos

struct SwipeView: View {
    @ObservedObject var photoLibraryManager: PhotoLibraryManager
    @State private var currentIndex = 0
    @State private var dragOffset = CGSize.zero
    @State private var showingDeleteConfirmation = false
    @State private var showingKeepConfirmation = false
    @State private var photosToDelete: [PhotoToReview] = []
    @State private var showingBatchActions = false
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.black.ignoresSafeArea()
                
                if photoLibraryManager.photosToReview.isEmpty {
                    emptyStateView
                } else {
                    photoStackView
                }
            }
            .navigationTitle("Review Photos")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Back") {
                        // Handle back navigation
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>("Batch") {
                        showingBatchActions = true
                    }
                }
            }
        }
        .sheet(isPresented: $showingBatchActions) {
            BatchActionsView(photoLibraryManager: photoLibraryManager)
        }
        .alert("Delete Photo?", isPresented: $showingDeleteConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                deleteCurrentPhoto()
            }
        } message: {
            Text("This action cannot be undone.")
        }
        .alert("Keep Photo?", isPresented: $showingKeepConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Keep") {
                keepCurrentPhoto()
            }
        } message: {
            Text("This photo will be removed from the review list.")
        }
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(.green)
            
            Text("All Done!")
                .font(.title)
                .fontWeight(.bold)
            
            Text("No more photos to review")
                .font(.body)
                .foregroundColor(.secondary)
        }
    }
    
    private var photoStackView: some View {
        ZStack {
            // Background cards
            ForEach(Array(photoLibraryManager.photosToReview.enumerated()), id: \.element.id) { index, photo in
                if index >= currentIndex && index < currentIndex + 3 {
                    PhotoCardView(photo: photo, isTopCard: index == currentIndex)
                        .offset(
                            x: index == currentIndex ? dragOffset.width : 0,
                            y: CGFloat(index - currentIndex) * 10
                        )
                        .scaleEffect(index == currentIndex ? 1.0 : 0.95 - CGFloat(index - currentIndex) * 0.05)
                        .opacity(index == currentIndex ? 1.0 : 0.8 - Double(index - currentIndex) * 0.2)
                        .animation(.spring(response: 0.5, dampingFraction: 0.8), value: dragOffset)
                        .gesture(
                            index == currentIndex ? 
                            DragGesture()
                                .onChanged { value in
                                    dragOffset = value.translation
                                }
                                .onEnded { value in
                                    handleSwipeGesture(translation: value.translation)
                                }
                            : nil
                        )
                }
            }
            
            // Action buttons
            VStack {
                Spacer()
                
                HStack(spacing: 60) {
                    // Keep button
                    Button(action: {
                        showingKeepConfirmation = true
                    }) {
                        Image(systemName: "heart.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .frame(width: 60, height: 60)
                            .background(Color.green)
                            .clipShape(Circle())
                            .shadow(radius: 5)
                    }
                    
                    // Delete button
                    Button(action: {
                        showingDeleteConfirmation = true
                    }) {
                        Image(systemName: "trash.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .frame(width: 60, height: 60)
                            .background(Color.red)
                            .clipShape(Circle())
                            .shadow(radius: 5)
                    }
                }
                .padding(.bottom, 50)
            }
        }
    }
    
    private func handleSwipeGesture(translation: CGSize) {
        let threshold: CGFloat = 100
        
        if translation.x > threshold {
            // Swipe right - keep photo
            keepCurrentPhoto()
        } else if translation.x < -threshold {
            // Swipe left - delete photo
            showingDeleteConfirmation = true
        } else {
            // Return to center
            withAnimation(.spring()) {
                dragOffset = .zero
            }
        }
    }
    
    private func keepCurrentPhoto() {
        guard currentIndex < photoLibraryManager.photosToReview.count else { return }
        
        withAnimation(.easeInOut(duration: 0.3)) {
            dragOffset = CGSize(width: 300, height: 0)
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            photoLibraryManager.photosToReview.remove(at: currentIndex)
            dragOffset = .zero
            
            if currentIndex >= photoLibraryManager.photosToReview.count {
                currentIndex = max(0, photoLibraryManager.photosToReview.count - 1)
            }
        }
    }
    
    private func deleteCurrentPhoto() {
        guard currentIndex < photoLibraryManager.photosToReview.count else { return }
        
        let photoToDelete = photoLibraryManager.photosToReview[currentIndex]
        
        withAnimation(.easeInOut(duration: 0.3)) {
            dragOffset = CGSize(width: -300, height: 0)
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            Task {
                let success = await photoLibraryManager.deletePhotos([photoToDelete])
                
                await MainActor.run {
                    if success {
                        if currentIndex < photoLibraryManager.photosToReview.count {
                            photoLibraryManager.photosToReview.remove(at: currentIndex)
                        }
                    }
                    
                    dragOffset = .zero
                    
                    if currentIndex >= photoLibraryManager.photosToReview.count {
                        currentIndex = max(0, photoLibraryManager.photosToReview.count - 1)
                    }
                }
            }
        }
    }
}

struct PhotoCardView: View {
    let photo: PhotoToReview
    let isTopCard: Bool
    @State private var image: UIImage?
    
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.white)
                .shadow(radius: isTopCard ? 10 : 5)
            
            VStack(spacing: 0) {
                // Photo
                ZStack {
                    if let image = image {
                        Image(uiImage: image)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(height: 400)
                            .clipped()
                    } else {
                        Rectangle()
                            .fill(Color.gray.opacity(0.3))
                            .frame(height: 400)
                            .overlay(
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                            )
                    }
                }
                .cornerRadius(20, corners: [.topLeft, .topRight])
                
                // Info section
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: photo.reason.icon)
                            .foregroundColor(photo.reason.color)
                            .font(.title2)
                        
                        VStack(alignment: .leading) {
                            Text(photo.reason.rawValue)
                                .font(.headline)
                                .fontWeight(.semibold)
                            
                            Text("Confidence: \(Int(photo.confidence * 100))%")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                    }
                    
                    if photo.reason == .duplicate, let duplicateGroup = photo.duplicateGroup {
                        Text("Found \(duplicateGroup.count) similar photos")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                    
                    // Photo details
                    HStack {
                        Text("\(photo.asset.pixelWidth) × \(photo.asset.pixelHeight)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        if let creationDate = photo.asset.creationDate {
                            Text(creationDate, style: .date)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding()
            }
        }
        .frame(width: 320, height: 500)
        .onAppear {
            loadImage()
        }
    }
    
    private func loadImage() {
        let options = PHImageRequestOptions()
        options.deliveryMode = .highQualityFormat
        options.resizeMode = .fast
        
        PHImageManager.default().requestImage(
            for: photo.asset,
            targetSize: CGSize(width: 320, height: 400),
            contentMode: .aspectFill,
            options: options
        ) { result, _ in
            DispatchQueue.main.async {
                self.image = result
            }
        }
    }
}

struct BatchActionsView: View {
    @ObservedObject var photoLibraryManager: PhotoLibraryManager
    @Environment(\.dismiss) private var dismiss
    @State private var selectedReasons: Set<ReviewReason> = []
    @State private var showingDeleteConfirmation = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Select categories to delete")
                    .font(.headline)
                    .padding()
                
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 15) {
                    ForEach(ReviewReason.allCases, id: \.self) { reason in
                        let count = photoLibraryManager.photosToReview.filter { $0.reason == reason }.count
                        
                        Button(action: {
                            if selectedReasons.contains(reason) {
                                selectedReasons.remove(reason)
                            } else {
                                selectedReasons.insert(reason)
                            }
                        }) {
                            VStack {
                                Image(systemName: reason.icon)
                                    .font(.title2)
                                    .foregroundColor(selectedReasons.contains(reason) ? .white : reason.color)
                                
                                Text(reason.rawValue)
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(selectedReasons.contains(reason) ? .white : .primary)
                                
                                Text("\(count) items")
                                    .font(.caption2)
                                    .foregroundColor(selectedReasons.contains(reason) ? .white.opacity(0.8) : .secondary)
                            }
                            .frame(height: 80)
                            .frame(maxWidth: .infinity)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(selectedReasons.contains(reason) ? reason.color : Color.gray.opacity(0.1))
                            )
                        }
                        .disabled(count == 0)
                    }
                }
                .padding(.horizontal)
                
                Spacer()
                
                if !selectedReasons.isEmpty {
                    Button("Delete Selected Categories") {
                        showingDeleteConfirmation = true
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(Color.red)
                    .cornerRadius(12)
                    .padding(.horizontal)
                }
            }
            .navigationTitle("Batch Actions")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
        .alert("Delete \(selectedReasons.count) categories?", isPresented: $showingDeleteConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                deleteBatchPhotos()
            }
        } message: {
            Text("This will delete all photos in the selected categories. This action cannot be undone.")
        }
    }
    
    private func deleteBatchPhotos() {
        let photosToDelete = photoLibraryManager.photosToReview.filter { selectedReasons.contains($0.reason) }
        
        Task {
            let success = await photoLibraryManager.deletePhotos(photosToDelete)
            
            await MainActor.run {
                if success {
                    dismiss()
                }
            }
        }
    }
}

extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

#Preview {
    SwipeView(photoLibraryManager: PhotoLibraryManager())
}
