//
//  PhotoLibraryManager.swift
//  PhotoCleaner
//
//  Created by 王博 on 2025/7/26.
//

import SwiftUI
import Photos
import AVFoundation

// MARK: - Data Models
struct PhotoToReview: Identifiable, Hashable {
    let id = UUID()
    let asset: PHAsset
    let reason: ReviewReason
    let confidence: Float
    let duplicateGroup: [PHAsset]?
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: PhotoToReview, rhs: PhotoToReview) -> Bool {
        lhs.id == rhs.id
    }
}

enum ReviewReason: String, CaseIterable {
    case duplicate = "Duplicate"
    case blur = "Blurry"
    case closedEyes = "Closed Eyes"
    case lowQuality = "Low Quality"
    case screenshot = "Screenshot"
    case largeVideo = "Large Video"
    case livePhoto = "Live Photo"
    
    var icon: String {
        switch self {
        case .duplicate: return "doc.on.doc"
        case .blur: return "eye.slash"
        case .closedEyes: return "eye.slash.circle"
        case .lowQuality: return "star.slash"
        case .screenshot: return "camera.viewfinder"
        case .largeVideo: return "video.badge.plus"
        case .livePhoto: return "livephoto"
        }
    }
    
    var color: Color {
        switch self {
        case .duplicate: return .orange
        case .blur: return .red
        case .closedEyes: return .purple
        case .lowQuality: return .gray
        case .screenshot: return .blue
        case .largeVideo: return .green
        case .livePhoto: return .cyan
        }
    }
}

// MARK: - Photo Library Manager
@MainActor
class PhotoLibraryManager: ObservableObject {
    @Published var photosToReview: [PhotoToReview] = []
    @Published var authorizationStatus: PHAuthorizationStatus = .notDetermined
    @Published var isScanning = false
    @Published var scanProgress: Float = 0.0
    @Published var scanStatusMessage = ""
    @Published var statistics = ScanStatistics()
    
    private let duplicateDetector = DuplicateDetector()
    private let blurDetector = BlurDetector()
    private let eyeDetector = EyeDetector()
    private let screenshotDetector = ScreenshotDetector()
    private let videoAnalyzer = VideoAnalyzer()
    private let livePhotoDetector = LivePhotoDetector()
    
    struct ScanStatistics {
        var totalPhotos = 0
        var duplicates = 0
        var blurryPhotos = 0
        var closedEyePhotos = 0
        var screenshots = 0
        var largeVideos = 0
        var livePhotos = 0
        var potentialSavings: Int64 = 0 // in bytes
    }
    
    init() {
        authorizationStatus = PHPhotoLibrary.authorizationStatus(for: .readWrite)
    }
    
    func requestPhotoLibraryPermission() {
        PHPhotoLibrary.requestAuthorization(for: .readWrite) { [weak self] status in
            DispatchQueue.main.async {
                self?.authorizationStatus = status
            }
        }
    }
    
    func startScanning() async {
        guard authorizationStatus == .authorized else { return }
        
        isScanning = true
        scanProgress = 0.0
        photosToReview.removeAll()
        statistics = ScanStatistics()
        
        do {
            // Fetch all assets
            scanStatusMessage = "Loading photo library..."
            let fetchOptions = PHFetchOptions()
            fetchOptions.includeHiddenAssets = false
            fetchOptions.includeAllBurstPhotos = false
            
            let allAssets = PHAsset.fetchAssets(with: fetchOptions)
            statistics.totalPhotos = allAssets.count
            
            guard statistics.totalPhotos > 0 else {
                scanStatusMessage = "No photos found"
                isScanning = false
                return
            }
            
            // Process assets in batches
            let batchSize = 50
            var processedCount = 0
            
            for i in stride(from: 0, to: allAssets.count, by: batchSize) {
                let endIndex = min(i + batchSize, allAssets.count)
                var batchAssets: [PHAsset] = []
                
                for j in i..<endIndex {
                    batchAssets.append(allAssets.object(at: j))
                }
                
                await processBatch(batchAssets)
                
                processedCount += batchAssets.count
                scanProgress = Float(processedCount) / Float(statistics.totalPhotos)
                scanStatusMessage = "Analyzed \(processedCount) of \(statistics.totalPhotos) items"
            }
            
            // Final processing
            scanStatusMessage = "Finalizing analysis..."
            await finalizeAnalysis()
            
            scanStatusMessage = "Scan complete! Found \(photosToReview.count) items to review"
            
        } catch {
            scanStatusMessage = "Error during scan: \(error.localizedDescription)"
        }
        
        isScanning = false
    }
    
    private func processBatch(_ assets: [PHAsset]) async {
        await withTaskGroup(of: Void.self) { group in
            for asset in assets {
                group.addTask { [weak self] in
                    await self?.analyzeAsset(asset)
                }
            }
        }
    }
    
    private func analyzeAsset(_ asset: PHAsset) async {
        // Skip if already processed
        if photosToReview.contains(where: { $0.asset.localIdentifier == asset.localIdentifier }) {
            return
        }
        
        var detectedIssues: [PhotoToReview] = []
        
        // Check for screenshots
        if await screenshotDetector.isScreenshot(asset: asset) {
            detectedIssues.append(PhotoToReview(
                asset: asset,
                reason: .screenshot,
                confidence: 0.9,
                duplicateGroup: nil
            ))
            statistics.screenshots += 1
        }
        
        // Check for large videos
        if asset.mediaType == .video {
            let videoSize = await videoAnalyzer.getVideoSize(asset: asset)
            if videoSize > 100 * 1024 * 1024 { // 100MB threshold
                detectedIssues.append(PhotoToReview(
                    asset: asset,
                    reason: .largeVideo,
                    confidence: 1.0,
                    duplicateGroup: nil
                ))
                statistics.largeVideos += 1
                statistics.potentialSavings += videoSize
            }
        }
        
        // Check for Live Photos
        if asset.mediaSubtypes.contains(.photoLive) {
            detectedIssues.append(PhotoToReview(
                asset: asset,
                reason: .livePhoto,
                confidence: 0.8,
                duplicateGroup: nil
            ))
            statistics.livePhotos += 1
        }
        
        // For images, check blur and closed eyes
        if asset.mediaType == .image {
            if let image = await loadImage(from: asset) {
                // Check for blur
                let blurScore = await blurDetector.calculateBlurScore(image: image)
                if blurScore < 100.0 { // Threshold for blur
                    detectedIssues.append(PhotoToReview(
                        asset: asset,
                        reason: .blur,
                        confidence: (100.0 - blurScore) / 100.0,
                        duplicateGroup: nil
                    ))
                    statistics.blurryPhotos += 1
                }
                
                // Check for closed eyes
                if await eyeDetector.hasClosedEyes(image: image) {
                    detectedIssues.append(PhotoToReview(
                        asset: asset,
                        reason: .closedEyes,
                        confidence: 0.7,
                        duplicateGroup: nil
                    ))
                    statistics.closedEyePhotos += 1
                }
            }
        }
        
        // Add detected issues to the main array
        for issue in detectedIssues {
            photosToReview.append(issue)
        }
    }
    
    private func finalizeAnalysis() async {
        // Find duplicates
        scanStatusMessage = "Finding duplicates..."
        let imageAssets = photosToReview.compactMap { $0.asset.mediaType == .image ? $0.asset : nil }
        let duplicateGroups = await duplicateDetector.findDuplicates(in: imageAssets)
        
        for group in duplicateGroups where group.count > 1 {
            // Keep the first one, mark others as duplicates
            for i in 1..<group.count {
                let duplicatePhoto = PhotoToReview(
                    asset: group[i],
                    reason: .duplicate,
                    confidence: 0.9,
                    duplicateGroup: group
                )
                photosToReview.append(duplicatePhoto)
                statistics.duplicates += 1
            }
        }
        
        // Sort by confidence (highest first)
        photosToReview.sort { $0.confidence > $1.confidence }
    }
    
    private func loadImage(from asset: PHAsset) async -> UIImage? {
        return await withCheckedContinuation { continuation in
            let options = PHImageRequestOptions()
            options.isSynchronous = false
            options.deliveryMode = .highQualityFormat
            options.resizeMode = .fast
            
            PHImageManager.default().requestImage(
                for: asset,
                targetSize: CGSize(width: 300, height: 300),
                contentMode: .aspectFit,
                options: options
            ) { image, _ in
                continuation.resume(returning: image)
            }
        }
    }
    
    func deletePhotos(_ photos: [PhotoToReview]) async -> Bool {
        let assets = photos.map { $0.asset }
        
        return await withCheckedContinuation { continuation in
            PHPhotoLibrary.shared().performChanges({
                PHAssetChangeRequest.deleteAssets(assets as NSArray)
            }) { success, error in
                if success {
                    DispatchQueue.main.async { [weak self] in
                        self?.photosToReview.removeAll { photo in
                            assets.contains(photo.asset)
                        }
                    }
                }
                continuation.resume(returning: success)
            }
        }
    }
}
