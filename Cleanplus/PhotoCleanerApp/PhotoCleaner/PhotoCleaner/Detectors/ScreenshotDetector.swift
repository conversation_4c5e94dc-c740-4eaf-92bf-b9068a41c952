//
//  ScreenshotDetector.swift
//  PhotoCleaner
//
//  Created by 王博 on 2025/7/26.
//

import UIKit
import Photos
import Vision

class ScreenshotDetector {
    
    func isScreenshot(asset: PHAsset) async -> Bool {
        // Check metadata first
        if await checkMetadataForScreenshot(asset: asset) {
            return true
        }
        
        // Check image characteristics
        if let image = await loadImage(from: asset) {
            return await analyzeImageForScreenshot(image: image)
        }
        
        return false
    }
    
    private func checkMetadataForScreenshot(asset: PHAsset) async -> Bool {
        return await withCheckedContinuation { continuation in
            let options = PHImageRequestOptions()
            options.isSynchronous = false
            
            PHImageManager.default().requestImageDataAndOrientation(for: asset, options: options) { data, _, _, info in
                guard let data = data,
                      let source = CGImageSourceCreateWithData(data, nil),
                      let properties = CGImageSourceCopyPropertiesAtIndex(source, 0, nil) as? [String: Any] else {
                    continuation.resume(returning: false)
                    return
                }
                
                // Check for screenshot indicators in metadata
                var isScreenshot = false
                
                // Check EXIF data
                if let exif = properties[kCGImagePropertyExifDictionary as String] as? [String: Any] {
                    // Screenshots often have specific software tags
                    if let software = exif[kCGImagePropertyExifSoftware as String] as? String {
                        isScreenshot = software.lowercased().contains("screenshot") ||
                                     software.lowercased().contains("ios") ||
                                     software.lowercased().contains("iphone")
                    }
                }
                
                // Check TIFF data
                if let tiff = properties[kCGImagePropertyTIFFDictionary as String] as? [String: Any] {
                    if let software = tiff[kCGImagePropertyTIFFSoftware as String] as? String {
                        isScreenshot = isScreenshot || software.lowercased().contains("screenshot")
                    }
                }
                
                continuation.resume(returning: isScreenshot)
            }
        }
    }
    
    private func analyzeImageForScreenshot(image: UIImage) async -> Bool {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                var screenshotScore = 0
                
                // Check aspect ratio (common screenshot ratios)
                let aspectRatio = image.size.width / image.size.height
                let commonScreenRatios: [CGFloat] = [16.0/9.0, 19.5/9.0, 18/9.0, 4.0/3.0, 3.0/2.0]
                
                for ratio in commonScreenRatios {
                    if abs(aspectRatio - ratio) < 0.1 || abs(aspectRatio - 1/ratio) < 0.1 {
                        screenshotScore += 2
                        break
                    }
                }
                
                // Check for UI elements using text detection
                self.detectUIElements(in: image) { hasUIElements in
                    if hasUIElements {
                        screenshotScore += 3
                    }
                    
                    // Check for sharp edges (screenshots are usually very sharp)
                    let sharpnessScore = self.calculateSharpness(image: image)
                    if sharpnessScore > 200 { // High sharpness threshold
                        screenshotScore += 2
                    }
                    
                    // Check for uniform colors (status bars, navigation bars)
                    if self.hasUniformColorRegions(image: image) {
                        screenshotScore += 2
                    }
                    
                    // Screenshot if score is high enough
                    continuation.resume(returning: screenshotScore >= 4)
                }
            }
        }
    }
    
    private func detectUIElements(in image: UIImage, completion: @escaping (Bool) -> Void) {
        guard let cgImage = image.cgImage else {
            completion(false)
            return
        }
        
        let request = VNRecognizeTextRequest { request, error in
            guard error == nil,
                  let results = request.results as? [VNRecognizedTextObservation] else {
                completion(false)
                return
            }
            
            var hasUIText = false
            let uiKeywords = ["settings", "back", "done", "cancel", "ok", "menu", "home", "search", "share", "edit", "save", "delete", "add", "next", "previous", "close", "open"]
            
            for observation in results {
                guard let topCandidate = observation.topCandidates(1).first else { continue }
                let text = topCandidate.string.lowercased()
                
                for keyword in uiKeywords {
                    if text.contains(keyword) {
                        hasUIText = true
                        break
                    }
                }
                
                if hasUIText { break }
            }
            
            completion(hasUIText)
        }
        
        request.recognitionLevel = .fast
        request.usesLanguageCorrection = false
        
        let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
        
        do {
            try handler.perform([request])
        } catch {
            completion(false)
        }
    }
    
    private func calculateSharpness(image: UIImage) -> Float {
        guard let cgImage = image.cgImage else { return 0.0 }
        
        // Convert to grayscale
        let width = cgImage.width
        let height = cgImage.height
        let bytesPerPixel = 1
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8
        
        var pixelData = [UInt8](repeating: 0, count: width * height)
        
        let colorSpace = CGColorSpaceCreateDeviceGray()
        let context = CGContext(
            data: &pixelData,
            width: width,
            height: height,
            bitsPerComponent: bitsPerComponent,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.none.rawValue
        )
        
        context?.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
        
        // Calculate gradient magnitude
        var gradientSum: Float = 0
        let floatPixels = pixelData.map { Float($0) }
        
        for y in 1..<(height - 1) {
            for x in 1..<(width - 1) {
                let index = y * width + x
                
                // Sobel operators
                let gx = floatPixels[index - width - 1] * (-1) + floatPixels[index - width + 1] * 1 +
                        floatPixels[index - 1] * (-2) + floatPixels[index + 1] * 2 +
                        floatPixels[index + width - 1] * (-1) + floatPixels[index + width + 1] * 1
                
                let gy = floatPixels[index - width - 1] * (-1) + floatPixels[index - width] * (-2) + floatPixels[index - width + 1] * (-1) +
                        floatPixels[index + width - 1] * 1 + floatPixels[index + width] * 2 + floatPixels[index + width + 1] * 1
                
                let magnitude = sqrt(gx * gx + gy * gy)
                gradientSum += magnitude
            }
        }
        
        return gradientSum / Float((width - 2) * (height - 2))
    }
    
    private func hasUniformColorRegions(image: UIImage) -> Bool {
        guard let cgImage = image.cgImage else { return false }
        
        let width = cgImage.width
        let height = cgImage.height
        
        // Check top and bottom regions for uniform colors (status bar, navigation bar)
        let regionHeight = height / 10 // Check top and bottom 10%
        
        // Sample colors from top region
        let topColors = sampleColors(from: cgImage, region: CGRect(x: 0, y: 0, width: width, height: regionHeight))
        let bottomColors = sampleColors(from: cgImage, region: CGRect(x: 0, y: height - regionHeight, width: width, height: regionHeight))
        
        return isUniformColorRegion(colors: topColors) || isUniformColorRegion(colors: bottomColors)
    }
    
    private func sampleColors(from cgImage: CGImage, region: CGRect) -> [UIColor] {
        let width = Int(region.width)
        let height = Int(region.height)
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8
        
        var pixelData = [UInt8](repeating: 0, count: width * height * bytesPerPixel)
        
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let context = CGContext(
            data: &pixelData,
            width: width,
            height: height,
            bitsPerComponent: bitsPerComponent,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
        )
        
        context?.draw(cgImage, in: CGRect(x: -region.origin.x, y: -region.origin.y, width: cgImage.width, height: cgImage.height))
        
        var colors: [UIColor] = []
        let sampleStep = max(1, width / 10) // Sample every 10th pixel
        
        for y in stride(from: 0, to: height, by: sampleStep) {
            for x in stride(from: 0, to: width, by: sampleStep) {
                let index = (y * width + x) * bytesPerPixel
                let r = CGFloat(pixelData[index]) / 255.0
                let g = CGFloat(pixelData[index + 1]) / 255.0
                let b = CGFloat(pixelData[index + 2]) / 255.0
                let a = CGFloat(pixelData[index + 3]) / 255.0
                
                colors.append(UIColor(red: r, green: g, blue: b, alpha: a))
            }
        }
        
        return colors
    }
    
    private func isUniformColorRegion(colors: [UIColor]) -> Bool {
        guard colors.count > 1 else { return false }
        
        let threshold: CGFloat = 0.1
        let firstColor = colors[0]
        var r1: CGFloat = 0, g1: CGFloat = 0, b1: CGFloat = 0, a1: CGFloat = 0
        firstColor.getRed(&r1, green: &g1, blue: &b1, alpha: &a1)
        
        var uniformCount = 0
        
        for color in colors {
            var r2: CGFloat = 0, g2: CGFloat = 0, b2: CGFloat = 0, a2: CGFloat = 0
            color.getRed(&r2, green: &g2, blue: &b2, alpha: &a2)
            
            let distance = sqrt(pow(r1 - r2, 2) + pow(g1 - g2, 2) + pow(b1 - b2, 2))
            if distance < threshold {
                uniformCount += 1
            }
        }
        
        return Float(uniformCount) / Float(colors.count) > 0.8 // 80% of colors are similar
    }
    
    private func loadImage(from asset: PHAsset) async -> UIImage? {
        return await withCheckedContinuation { continuation in
            let options = PHImageRequestOptions()
            options.isSynchronous = false
            options.deliveryMode = .highQualityFormat
            options.resizeMode = .fast
            
            PHImageManager.default().requestImage(
                for: asset,
                targetSize: CGSize(width: 300, height: 300),
                contentMode: .aspectFit,
                options: options
            ) { image, _ in
                continuation.resume(returning: image)
            }
        }
    }
}
