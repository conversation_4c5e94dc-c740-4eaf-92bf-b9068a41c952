//
//  BlurDetector.swift
//  PhotoCleaner
//
//  Created by 王博 on 2025/7/26.
//

import UIKit
import CoreImage
import Accelerate

class BlurDetector {
    private let blurThreshold: Float = 100.0
    
    func calculateBlurScore(image: UIImage) async -> Float {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                let score = self.calculateLaplacianVariance(image: image)
                continuation.resume(returning: score)
            }
        }
    }
    
    private func calculateLaplacianVariance(image: UIImage) -> Float {
        guard let cgImage = image.cgImage else { return 0.0 }
        
        // Convert to grayscale
        let width = cgImage.width
        let height = cgImage.height
        let bytesPerPixel = 1
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8
        
        var pixelData = [UInt8](repeating: 0, count: width * height)
        
        let colorSpace = CGColorSpaceCreateDeviceGray()
        let context = CGContext(
            data: &pixelData,
            width: width,
            height: height,
            bitsPerComponent: bitsPerComponent,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.none.rawValue
        )
        
        context?.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
        
        // Apply Laplacian kernel
        let laplacianKernel: [Float] = [
            0, -1, 0,
            -1, 4, -1,
            0, -1, 0
        ]
        
        var result = [Float](repeating: 0, count: width * height)
        var floatPixels = pixelData.map { Float($0) }
        
        // Apply convolution
        for y in 1..<(height - 1) {
            for x in 1..<(width - 1) {
                var sum: Float = 0
                
                for ky in -1...1 {
                    for kx in -1...1 {
                        let pixelIndex = (y + ky) * width + (x + kx)
                        let kernelIndex = (ky + 1) * 3 + (kx + 1)
                        sum += floatPixels[pixelIndex] * laplacianKernel[kernelIndex]
                    }
                }
                
                result[y * width + x] = sum
            }
        }
        
        // Calculate variance
        let mean = result.reduce(0, +) / Float(result.count)
        let variance = result.map { pow($0 - mean, 2) }.reduce(0, +) / Float(result.count)
        
        return variance
    }
    
    // Alternative method using Core Image
    func calculateBlurScoreUsingCoreImage(image: UIImage) async -> Float {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                guard let ciImage = CIImage(image: image) else {
                    continuation.resume(returning: 0.0)
                    return
                }
                
                let context = CIContext()
                
                // Apply Gaussian blur
                guard let blurFilter = CIFilter(name: "CIGaussianBlur") else {
                    continuation.resume(returning: 0.0)
                    return
                }
                
                blurFilter.setValue(ciImage, forKey: kCIInputImageKey)
                blurFilter.setValue(1.0, forKey: kCIInputRadiusKey)
                
                guard let blurredImage = blurFilter.outputImage else {
                    continuation.resume(returning: 0.0)
                    return
                }
                
                // Calculate difference between original and blurred
                guard let differenceFilter = CIFilter(name: "CIDifferenceBlendMode") else {
                    continuation.resume(returning: 0.0)
                    return
                }
                
                differenceFilter.setValue(ciImage, forKey: kCIInputImageKey)
                differenceFilter.setValue(blurredImage, forKey: kCIInputBackgroundImageKey)
                
                guard let differenceImage = differenceFilter.outputImage else {
                    continuation.resume(returning: 0.0)
                    return
                }
                
                // Convert to grayscale and calculate average intensity
                guard let grayscaleFilter = CIFilter(name: "CIColorControls") else {
                    continuation.resume(returning: 0.0)
                    return
                }
                
                grayscaleFilter.setValue(differenceImage, forKey: kCIInputImageKey)
                grayscaleFilter.setValue(0.0, forKey: kCIInputSaturationKey)
                
                guard let finalImage = grayscaleFilter.outputImage else {
                    continuation.resume(returning: 0.0)
                    return
                }
                
                // Calculate average pixel intensity
                let extent = finalImage.extent
                guard let cgImage = context.createCGImage(finalImage, from: extent) else {
                    continuation.resume(returning: 0.0)
                    return
                }
                
                let score = self.calculateAverageIntensity(cgImage: cgImage)
                continuation.resume(returning: score * 1000) // Scale for better comparison
            }
        }
    }
    
    private func calculateAverageIntensity(cgImage: CGImage) -> Float {
        let width = cgImage.width
        let height = cgImage.height
        let bytesPerPixel = 1
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8
        
        var pixelData = [UInt8](repeating: 0, count: width * height)
        
        let colorSpace = CGColorSpaceCreateDeviceGray()
        let context = CGContext(
            data: &pixelData,
            width: width,
            height: height,
            bitsPerComponent: bitsPerComponent,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.none.rawValue
        )
        
        context?.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
        
        let sum = pixelData.reduce(0) { $0 + Int($1) }
        return Float(sum) / Float(pixelData.count) / 255.0
    }
    
    func isBlurry(image: UIImage) async -> Bool {
        let score = await calculateBlurScore(image: image)
        return score < blurThreshold
    }
    
    // Batch processing for multiple images
    func analyzeBlurInBatch(images: [UIImage]) async -> [Float] {
        return await withTaskGroup(of: (Int, Float).self, returning: [Float].self) { group in
            for (index, image) in images.enumerated() {
                group.addTask { [weak self] in
                    let score = await self?.calculateBlurScore(image: image) ?? 0.0
                    return (index, score)
                }
            }
            
            var results = Array(repeating: Float(0.0), count: images.count)
            for await (index, score) in group {
                results[index] = score
            }
            return results
        }
    }
}
