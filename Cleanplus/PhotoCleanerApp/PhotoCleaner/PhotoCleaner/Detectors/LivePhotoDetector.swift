//
//  LivePhotoDetector.swift
//  PhotoCleaner
//
//  Created by 王博 on 2025/7/26.
//

import Foundation
import Photos
import PhotosUI

class LivePhotoDetector {
    
    struct LivePhotoInfo {
        let asset: PHAsset
        let stillImageSize: Int64
        let videoSize: Int64
        let totalSize: Int64
        let duration: TimeInterval
        let hasMotion: Bool
    }
    
    func isLivePhoto(asset: PHAsset) -> Bool {
        return asset.mediaSubtypes.contains(.photoLive)
    }
    
    func findAllLivePhotos(in assets: [PHAsset]) -> [PHAsset] {
        return assets.filter { isLivePhoto(asset: $0) }
    }
    
    func getLivePhotoInfo(asset: PHAsset) async -> LivePhotoInfo? {
        guard isLivePhoto(asset: asset) else { return nil }
        
        return await withCheckedContinuation { continuation in
            let options = PHLivePhotoRequestOptions()
            options.isNetworkAccessAllowed = true
            options.deliveryMode = .highQualityFormat
            
            PHImageManager.default().requestLivePhoto(for: asset, targetSize: PHImageManagerMaximumSize, contentMode: .aspectFit, options: options) { livePhoto, info in
                guard let livePhoto = livePhoto else {
                    continuation.resume(returning: nil)
                    return
                }
                
                Task {
                    // Get still image size
                    let stillImageSize = await self.getStillImageSize(for: asset)
                    
                    // Get video component size
                    let videoSize = await self.getVideoComponentSize(for: asset)
                    
                    // Get duration (Live Photos are typically 3 seconds)
                    let duration = asset.duration
                    
                    // Analyze motion
                    let hasMotion = await self.analyzeMotion(in: livePhoto)
                    
                    let info = LivePhotoInfo(
                        asset: asset,
                        stillImageSize: stillImageSize,
                        videoSize: videoSize,
                        totalSize: stillImageSize + videoSize,
                        duration: duration,
                        hasMotion: hasMotion
                    )
                    
                    continuation.resume(returning: info)
                }
            }
        }
    }
    
    private func getStillImageSize(for asset: PHAsset) async -> Int64 {
        return await withCheckedContinuation { continuation in
            let options = PHImageRequestOptions()
            options.isSynchronous = false
            options.deliveryMode = .highQualityFormat
            
            PHImageManager.default().requestImageDataAndOrientation(for: asset, options: options) { data, _, _, _ in
                let size = Int64(data?.count ?? 0)
                continuation.resume(returning: size)
            }
        }
    }
    
    private func getVideoComponentSize(for asset: PHAsset) async -> Int64 {
        return await withCheckedContinuation { continuation in
            let options = PHVideoRequestOptions()
            options.isNetworkAccessAllowed = true
            options.deliveryMode = .highQualityFormat
            
            PHImageManager.default().requestAVAsset(forVideo: asset, options: options) { avAsset, _, _ in
                guard let urlAsset = avAsset as? AVURLAsset else {
                    continuation.resume(returning: 0)
                    return
                }
                
                do {
                    let resourceValues = try urlAsset.url.resourceValues(forKeys: [.fileSizeKey])
                    let fileSize = Int64(resourceValues.fileSize ?? 0)
                    continuation.resume(returning: fileSize)
                } catch {
                    continuation.resume(returning: 0)
                }
            }
        }
    }
    
    private func analyzeMotion(in livePhoto: PHLivePhoto) async -> Bool {
        // This is a simplified motion detection
        // In a real implementation, you might analyze the video frames
        // For now, we'll assume Live Photos with longer duration have more motion
        return true // Most Live Photos have some motion
    }
    
    func findDuplicateLivePhotos(in assets: [PHAsset]) async -> [[PHAsset]] {
        let livePhotos = findAllLivePhotos(in: assets)
        var duplicateGroups: [[PHAsset]] = []
        var processed: Set<String> = []
        
        for i in 0..<livePhotos.count {
            let asset1 = livePhotos[i]
            if processed.contains(asset1.localIdentifier) { continue }
            
            var duplicateGroup: [PHAsset] = [asset1]
            
            for j in (i+1)..<livePhotos.count {
                let asset2 = livePhotos[j]
                if processed.contains(asset2.localIdentifier) { continue }
                
                if await areLivePhotosSimilar(asset1: asset1, asset2: asset2) {
                    duplicateGroup.append(asset2)
                    processed.insert(asset2.localIdentifier)
                }
            }
            
            if duplicateGroup.count > 1 {
                duplicateGroups.append(duplicateGroup)
            }
            
            processed.insert(asset1.localIdentifier)
        }
        
        return duplicateGroups
    }
    
    private func areLivePhotosSimilar(asset1: PHAsset, asset2: PHAsset) async -> Bool {
        // Compare creation dates (within 5 seconds)
        if let date1 = asset1.creationDate, let date2 = asset2.creationDate {
            let timeDiff = abs(date1.timeIntervalSince(date2))
            if timeDiff > 5.0 { return false }
        }
        
        // Compare locations if available
        if let location1 = asset1.location, let location2 = asset2.location {
            let distance = location1.distance(from: location2)
            if distance > 100 { return false } // 100 meters threshold
        }
        
        // Compare image dimensions
        if abs(asset1.pixelWidth - asset2.pixelWidth) > 100 || abs(asset1.pixelHeight - asset2.pixelHeight) > 100 {
            return false
        }
        
        // If all checks pass, they might be similar
        return true
    }
    
    func findLivePhotosWithMinimalMotion(in assets: [PHAsset]) async -> [PHAsset] {
        let livePhotos = findAllLivePhotos(in: assets)
        var minimalMotionPhotos: [PHAsset] = []
        
        await withTaskGroup(of: (PHAsset, Bool).self) { group in
            for asset in livePhotos {
                group.addTask { [weak self] in
                    guard let info = await self?.getLivePhotoInfo(asset: asset) else {
                        return (asset, false)
                    }
                    return (asset, !info.hasMotion)
                }
            }
            
            for await (asset, hasMinimalMotion) in group {
                if hasMinimalMotion {
                    minimalMotionPhotos.append(asset)
                }
            }
        }
        
        return minimalMotionPhotos
    }
    
    func convertLivePhotoToStillImage(asset: PHAsset) async -> Bool {
        // This would require creating a new asset from the still image component
        // and deleting the original Live Photo
        // Implementation would depend on specific requirements and user permissions
        return false // Placeholder - actual implementation needed
    }
    
    func estimateLivePhotoStorageSavings(for assets: [PHAsset]) async -> Int64 {
        let livePhotos = findAllLivePhotos(in: assets)
        var totalSavings: Int64 = 0
        
        await withTaskGroup(of: Int64.self) { group in
            for asset in livePhotos {
                group.addTask { [weak self] in
                    guard let info = await self?.getLivePhotoInfo(asset: asset) else {
                        return 0
                    }
                    // Savings would be the video component size if converted to still
                    return info.videoSize
                }
            }
            
            for await savings in group {
                totalSavings += savings
            }
        }
        
        return totalSavings
    }
    
    func analyzeLivePhotoQuality(asset: PHAsset) async -> Float {
        guard let info = await getLivePhotoInfo(asset: asset) else { return 0.0 }
        
        var qualityScore: Float = 0.0
        
        // Size score (0-30 points)
        if info.stillImageSize > 5 * 1024 * 1024 { // > 5MB
            qualityScore += 30
        } else if info.stillImageSize > 2 * 1024 * 1024 { // > 2MB
            qualityScore += 20
        } else {
            qualityScore += 10
        }
        
        // Motion score (0-40 points)
        if info.hasMotion {
            qualityScore += 40
        } else {
            qualityScore += 10 // Minimal motion reduces quality score
        }
        
        // Duration score (0-30 points)
        if info.duration >= 2.5 {
            qualityScore += 30
        } else if info.duration >= 2.0 {
            qualityScore += 20
        } else {
            qualityScore += 10
        }
        
        return min(qualityScore, 100.0)
    }
    
    func groupLivePhotosByLocation(assets: [PHAsset]) -> [String: [PHAsset]] {
        let livePhotos = findAllLivePhotos(in: assets)
        var locationGroups: [String: [PHAsset]] = [:]
        
        for asset in livePhotos {
            let locationKey: String
            
            if let location = asset.location {
                // Group by approximate location (rounded to ~100m precision)
                let lat = round(location.coordinate.latitude * 1000) / 1000
                let lon = round(location.coordinate.longitude * 1000) / 1000
                locationKey = "\(lat),\(lon)"
            } else {
                locationKey = "no_location"
            }
            
            if locationGroups[locationKey] == nil {
                locationGroups[locationKey] = []
            }
            locationGroups[locationKey]?.append(asset)
        }
        
        return locationGroups
    }
}
