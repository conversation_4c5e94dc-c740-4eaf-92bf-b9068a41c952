//
//  VideoAnalyzer.swift
//  PhotoCleaner
//
//  Created by 王博 on 2025/7/26.
//

import Foundation
import Photos
import AVFoundation

class VideoAnalyzer {
    
    struct VideoInfo {
        let size: Int64 // in bytes
        let duration: TimeInterval
        let resolution: CGSize
        let frameRate: Float
        let bitRate: Int64
        let codec: String?
    }
    
    func getVideoSize(asset: PHAsset) async -> Int64 {
        guard asset.mediaType == .video else { return 0 }
        
        return await withCheckedContinuation { continuation in
            let options = PHVideoRequestOptions()
            options.isNetworkAccessAllowed = true
            options.deliveryMode = .highQualityFormat
            
            PHImageManager.default().requestAVAsset(forVideo: asset, options: options) { avAsset, _, _ in
                guard let urlAsset = avAsset as? AVURLAsset else {
                    continuation.resume(returning: 0)
                    return
                }
                
                do {
                    let resourceValues = try urlAsset.url.resourceValues(forKeys: [.fileSizeKey])
                    let fileSize = resourceValues.fileSize ?? 0
                    continuation.resume(returning: Int64(fileSize))
                } catch {
                    continuation.resume(returning: 0)
                }
            }
        }
    }
    
    func getVideoInfo(asset: PHAsset) async -> VideoInfo? {
        guard asset.mediaType == .video else { return nil }
        
        return await withCheckedContinuation { continuation in
            let options = PHVideoRequestOptions()
            options.isNetworkAccessAllowed = true
            options.deliveryMode = .highQualityFormat
            
            PHImageManager.default().requestAVAsset(forVideo: asset, options: options) { avAsset, _, _ in
                guard let urlAsset = avAsset as? AVURLAsset else {
                    continuation.resume(returning: nil)
                    return
                }
                
                Task {
                    do {
                        // Get file size
                        let resourceValues = try urlAsset.url.resourceValues(forKeys: [.fileSizeKey])
                        let fileSize = Int64(resourceValues.fileSize ?? 0)
                        
                        // Get duration
                        let duration = urlAsset.duration.seconds
                        
                        // Get video track information
                        let videoTracks = try await urlAsset.loadTracks(withMediaType: .video)
                        guard let videoTrack = videoTracks.first else {
                            continuation.resume(returning: nil)
                            return
                        }
                        
                        // Get resolution
                        let naturalSize = try await videoTrack.load(.naturalSize)
                        let preferredTransform = try await videoTrack.load(.preferredTransform)
                        let resolution = naturalSize.applying(preferredTransform)
                        let correctedResolution = CGSize(width: abs(resolution.width), height: abs(resolution.height))
                        
                        // Get frame rate
                        let nominalFrameRate = try await videoTrack.load(.nominalFrameRate)
                        
                        // Estimate bit rate
                        let bitRate = duration > 0 ? Int64(Double(fileSize * 8) / duration) : 0
                        
                        // Get codec information
                        let formatDescriptions = try await videoTrack.load(.formatDescriptions)
                        var codec: String?
                        if let formatDescription = formatDescriptions.first {
                            let mediaSubType = CMFormatDescriptionGetMediaSubType(formatDescription)
                            codec = self.codecStringFromFourCC(mediaSubType)
                        }
                        
                        let videoInfo = VideoInfo(
                            size: fileSize,
                            duration: duration,
                            resolution: correctedResolution,
                            frameRate: nominalFrameRate,
                            bitRate: bitRate,
                            codec: codec
                        )
                        
                        continuation.resume(returning: videoInfo)
                        
                    } catch {
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    
    private func codecStringFromFourCC(_ fourCC: FourCharCode) -> String {
        let bytes = [
            UInt8((fourCC >> 24) & 0xFF),
            UInt8((fourCC >> 16) & 0xFF),
            UInt8((fourCC >> 8) & 0xFF),
            UInt8(fourCC & 0xFF)
        ]
        
        return String(bytes: bytes, encoding: .ascii) ?? "Unknown"
    }
    
    func findLargeVideos(in assets: [PHAsset], sizeThreshold: Int64 = 100 * 1024 * 1024) async -> [PHAsset] {
        let videoAssets = assets.filter { $0.mediaType == .video }
        var largeVideos: [PHAsset] = []
        
        await withTaskGroup(of: (PHAsset, Int64).self) { group in
            for asset in videoAssets {
                group.addTask { [weak self] in
                    let size = await self?.getVideoSize(asset: asset) ?? 0
                    return (asset, size)
                }
            }
            
            for await (asset, size) in group {
                if size > sizeThreshold {
                    largeVideos.append(asset)
                }
            }
        }
        
        return largeVideos
    }
    
    func analyzeDuplicateVideos(in assets: [PHAsset]) async -> [[PHAsset]] {
        let videoAssets = assets.filter { $0.mediaType == .video }
        var duplicateGroups: [[PHAsset]] = []
        var processedAssets: Set<String> = []
        
        // Group by duration first (quick filter)
        var durationGroups: [Int: [PHAsset]] = [:]
        
        for asset in videoAssets {
            let durationKey = Int(asset.duration)
            if durationGroups[durationKey] == nil {
                durationGroups[durationKey] = []
            }
            durationGroups[durationKey]?.append(asset)
        }
        
        // Analyze groups with multiple videos of same duration
        for (_, group) in durationGroups where group.count > 1 {
            let duplicates = await findDuplicatesInGroup(group)
            duplicateGroups.append(contentsOf: duplicates)
        }
        
        return duplicateGroups
    }
    
    private func findDuplicatesInGroup(_ assets: [PHAsset]) async -> [[PHAsset]] {
        var duplicateGroups: [[PHAsset]] = []
        var processed: Set<String> = []
        
        for i in 0..<assets.count {
            let asset1 = assets[i]
            if processed.contains(asset1.localIdentifier) { continue }
            
            var duplicateGroup: [PHAsset] = [asset1]
            
            for j in (i+1)..<assets.count {
                let asset2 = assets[j]
                if processed.contains(asset2.localIdentifier) { continue }
                
                if await areVideosSimilar(asset1: asset1, asset2: asset2) {
                    duplicateGroup.append(asset2)
                    processed.insert(asset2.localIdentifier)
                }
            }
            
            if duplicateGroup.count > 1 {
                duplicateGroups.append(duplicateGroup)
            }
            
            processed.insert(asset1.localIdentifier)
        }
        
        return duplicateGroups
    }
    
    private func areVideosSimilar(asset1: PHAsset, asset2: PHAsset) async -> Bool {
        guard let info1 = await getVideoInfo(asset: asset1),
              let info2 = await getVideoInfo(asset: asset2) else {
            return false
        }
        
        // Check duration similarity (within 1 second)
        let durationDiff = abs(info1.duration - info2.duration)
        if durationDiff > 1.0 { return false }
        
        // Check resolution similarity
        let resolutionDiff = abs(info1.resolution.width - info2.resolution.width) + abs(info1.resolution.height - info2.resolution.height)
        if resolutionDiff > 100 { return false }
        
        // Check file size similarity (within 10%)
        let sizeDiff = abs(info1.size - info2.size)
        let sizeThreshold = max(info1.size, info2.size) / 10
        if sizeDiff > sizeThreshold { return false }
        
        // If all checks pass, they're likely similar
        return true
    }
    
    func getVideoQualityScore(asset: PHAsset) async -> Float {
        guard let info = await getVideoInfo(asset: asset) else { return 0.0 }
        
        var score: Float = 0.0
        
        // Resolution score (0-40 points)
        let pixelCount = info.resolution.width * info.resolution.height
        if pixelCount >= 3840 * 2160 { // 4K
            score += 40
        } else if pixelCount >= 1920 * 1080 { // 1080p
            score += 30
        } else if pixelCount >= 1280 * 720 { // 720p
            score += 20
        } else {
            score += 10
        }
        
        // Frame rate score (0-20 points)
        if info.frameRate >= 60 {
            score += 20
        } else if info.frameRate >= 30 {
            score += 15
        } else if info.frameRate >= 24 {
            score += 10
        } else {
            score += 5
        }
        
        // Bit rate score (0-20 points)
        let bitRateMbps = Float(info.bitRate) / 1_000_000
        if bitRateMbps >= 50 {
            score += 20
        } else if bitRateMbps >= 20 {
            score += 15
        } else if bitRateMbps >= 10 {
            score += 10
        } else {
            score += 5
        }
        
        // Codec score (0-20 points)
        if let codec = info.codec {
            if codec.contains("hvc1") || codec.contains("hev1") { // HEVC/H.265
                score += 20
            } else if codec.contains("avc1") { // H.264
                score += 15
            } else {
                score += 10
            }
        }
        
        return min(score, 100.0) // Cap at 100
    }
    
    func estimateStorageSavings(for assets: [PHAsset]) async -> Int64 {
        var totalSavings: Int64 = 0
        
        await withTaskGroup(of: Int64.self) { group in
            for asset in assets {
                group.addTask { [weak self] in
                    return await self?.getVideoSize(asset: asset) ?? 0
                }
            }
            
            for await size in group {
                totalSavings += size
            }
        }
        
        return totalSavings
    }
}
