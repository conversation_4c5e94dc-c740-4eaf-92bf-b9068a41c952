//
//  DuplicateDetector.swift
//  PhotoCleaner
//
//  Created by 王博 on 2025/7/26.
//

import UIKit
import Photos
import CryptoKit

class DuplicateDetector {
    private let hammingDistanceThreshold = 5
    
    func findDuplicates(in assets: [PHAsset]) async -> [[PHAsset]] {
        var duplicateGroups: [[PHAsset]] = []
        var processedAssets: Set<String> = []
        
        // Create hash map for quick lookup
        var hashToAssets: [String: [PHAsset]] = [:]
        
        // Process assets in batches to avoid memory issues
        let batchSize = 20
        for i in stride(from: 0, to: assets.count, by: batchSize) {
            let endIndex = min(i + batchSize, assets.count)
            let batch = Array(assets[i..<endIndex])
            
            await processBatchForDuplicates(batch: batch, hashToAssets: &hashToAssets)
        }
        
        // Find duplicate groups
        for (_, assetGroup) in hashToAssets {
            if assetGroup.count > 1 {
                duplicateGroups.append(assetGroup)
            }
        }
        
        return duplicateGroups
    }
    
    private func processBatchForDuplicates(batch: [PHAsset], hashToAssets: inout [String: [PHAsset]]) async {
        await withTaskGroup(of: (PHAsset, String?).self) { group in
            for asset in batch {
                group.addTask { [weak self] in
                    let hash = await self?.generatePerceptualHash(for: asset)
                    return (asset, hash)
                }
            }
            
            for await (asset, hash) in group {
                if let hash = hash {
                    if hashToAssets[hash] == nil {
                        hashToAssets[hash] = []
                    }
                    hashToAssets[hash]?.append(asset)
                }
            }
        }
    }
    
    private func generatePerceptualHash(for asset: PHAsset) async -> String? {
        guard let image = await loadImage(from: asset) else { return nil }
        return generatePerceptualHash(image)
    }
    
    private func generatePerceptualHash(_ image: UIImage) -> String {
        // Resize to 8x8 grayscale
        guard let resizedImage = resizeImage(image, to: CGSize(width: 8, height: 8)),
              let cgImage = resizedImage.cgImage else {
            return ""
        }
        
        // Convert to grayscale and get pixel data
        let width = cgImage.width
        let height = cgImage.height
        let bytesPerPixel = 1
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8
        
        var pixelData = [UInt8](repeating: 0, count: width * height)
        
        let colorSpace = CGColorSpaceCreateDeviceGray()
        let context = CGContext(
            data: &pixelData,
            width: width,
            height: height,
            bitsPerComponent: bitsPerComponent,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.none.rawValue
        )
        
        context?.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
        
        // Calculate average pixel value
        let average = pixelData.reduce(0, +) / pixelData.count
        
        // Generate hash based on whether each pixel is above or below average
        var hash = ""
        for pixel in pixelData {
            hash += pixel > average ? "1" : "0"
        }
        
        return hash
    }
    
    private func resizeImage(_ image: UIImage, to size: CGSize) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 1.0)
        defer { UIGraphicsEndImageContext() }
        
        image.draw(in: CGRect(origin: .zero, size: size))
        return UIGraphicsGetImageFromCurrentImageContext()
    }
    
    private func loadImage(from asset: PHAsset) async -> UIImage? {
        return await withCheckedContinuation { continuation in
            let options = PHImageRequestOptions()
            options.isSynchronous = false
            options.deliveryMode = .highQualityFormat
            options.resizeMode = .fast
            
            PHImageManager.default().requestImage(
                for: asset,
                targetSize: CGSize(width: 300, height: 300),
                contentMode: .aspectFit,
                options: options
            ) { image, _ in
                continuation.resume(returning: image)
            }
        }
    }
    
    private func hammingDistance(_ hash1: String, _ hash2: String) -> Int {
        guard hash1.count == hash2.count else { return Int.max }
        
        var distance = 0
        for (char1, char2) in zip(hash1, hash2) {
            if char1 != char2 {
                distance += 1
            }
        }
        return distance
    }
    
    // Alternative method using SHA256 for exact duplicates
    func findExactDuplicates(in assets: [PHAsset]) async -> [[PHAsset]] {
        var hashToAssets: [String: [PHAsset]] = [:]
        
        for asset in assets {
            if let hash = await generateSHA256Hash(for: asset) {
                if hashToAssets[hash] == nil {
                    hashToAssets[hash] = []
                }
                hashToAssets[hash]?.append(asset)
            }
        }
        
        return hashToAssets.values.filter { $0.count > 1 }.map { Array($0) }
    }
    
    private func generateSHA256Hash(for asset: PHAsset) async -> String? {
        return await withCheckedContinuation { continuation in
            let options = PHImageRequestOptions()
            options.isSynchronous = false
            options.deliveryMode = .highQualityFormat
            
            PHImageManager.default().requestImageDataAndOrientation(for: asset, options: options) { data, _, _, _ in
                guard let data = data else {
                    continuation.resume(returning: nil)
                    return
                }
                
                let hash = SHA256.hash(data: data)
                let hashString = hash.compactMap { String(format: "%02x", $0) }.joined()
                continuation.resume(returning: hashString)
            }
        }
    }
}
