//
//  EyeDetector.swift
//  PhotoCleaner
//
//  Created by 王博 on 2025/7/26.
//

import UIKit
import Vision
import CoreImage

class EyeDetector {
    private let eyeAspectRatioThreshold: Float = 0.25
    
    func hasClosedEyes(image: UIImage) async -> Bool {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                self.detectClosedEyesUsingVision(image: image) { hasClosedEyes in
                    continuation.resume(returning: hasClosedEyes)
                }
            }
        }
    }
    
    private func detectClosedEyesUsingVision(image: UIImage, completion: @escaping (Bool) -> Void) {
        guard let cgImage = image.cgImage else {
            completion(false)
            return
        }
        
        let request = VNDetectFaceLandmarksRequest { request, error in
            guard error == nil,
                  let results = request.results as? [VNFaceObservation] else {
                completion(false)
                return
            }
            
            var hasClosedEyes = false
            
            for faceObservation in results {
                guard let landmarks = faceObservation.landmarks else { continue }
                
                // Check left eye
                if let leftEye = landmarks.leftEye {
                    let leftEAR = self.calculateEyeAspectRatio(eyePoints: leftEye.normalizedPoints)
                    if leftEAR < self.eyeAspectRatioThreshold {
                        hasClosedEyes = true
                        break
                    }
                }
                
                // Check right eye
                if let rightEye = landmarks.rightEye {
                    let rightEAR = self.calculateEyeAspectRatio(eyePoints: rightEye.normalizedPoints)
                    if rightEAR < self.eyeAspectRatioThreshold {
                        hasClosedEyes = true
                        break
                    }
                }
            }
            
            completion(hasClosedEyes)
        }
        
        let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
        
        do {
            try handler.perform([request])
        } catch {
            completion(false)
        }
    }
    
    private func calculateEyeAspectRatio(eyePoints: [CGPoint]) -> Float {
        guard eyePoints.count >= 6 else { return 1.0 }
        
        // Eye Aspect Ratio (EAR) calculation
        // EAR = (|p2-p6| + |p3-p5|) / (2 * |p1-p4|)
        // Where points are ordered as: outer corner, top points, inner corner, bottom points
        
        let points = eyePoints
        
        // Calculate vertical distances
        let vertical1 = distance(points[1], points[5])
        let vertical2 = distance(points[2], points[4])
        
        // Calculate horizontal distance
        let horizontal = distance(points[0], points[3])
        
        // Calculate EAR
        let ear = (vertical1 + vertical2) / (2.0 * horizontal)
        
        return Float(ear)
    }
    
    private func distance(_ point1: CGPoint, _ point2: CGPoint) -> Double {
        let dx = point1.x - point2.x
        let dy = point1.y - point2.y
        return sqrt(dx * dx + dy * dy)
    }
    
    // Alternative method using Core Image face detection
    func detectClosedEyesUsingCoreImage(image: UIImage) async -> Bool {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                guard let ciImage = CIImage(image: image) else {
                    continuation.resume(returning: false)
                    return
                }
                
                let context = CIContext()
                let detector = CIDetector(ofType: CIDetectorTypeFace, context: context, options: [
                    CIDetectorAccuracy: CIDetectorAccuracyHigh,
                    CIDetectorEyeBlink: true
                ])
                
                let features = detector?.features(in: ciImage) as? [CIFaceFeature] ?? []
                
                for feature in features {
                    if feature.leftEyeClosed || feature.rightEyeClosed {
                        continuation.resume(returning: true)
                        return
                    }
                }
                
                continuation.resume(returning: false)
            }
        }
    }
    
    // Batch processing for multiple images
    func analyzeEyesInBatch(images: [UIImage]) async -> [Bool] {
        return await withTaskGroup(of: (Int, Bool).self, returning: [Bool].self) { group in
            for (index, image) in images.enumerated() {
                group.addTask { [weak self] in
                    let hasClosedEyes = await self?.hasClosedEyes(image: image) ?? false
                    return (index, hasClosedEyes)
                }
            }
            
            var results = Array(repeating: false, count: images.count)
            for await (index, hasClosedEyes) in group {
                results[index] = hasClosedEyes
            }
            return results
        }
    }
    
    // Enhanced detection with confidence scoring
    func detectClosedEyesWithConfidence(image: UIImage) async -> (hasClosedEyes: Bool, confidence: Float) {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                guard let cgImage = image.cgImage else {
                    continuation.resume(returning: (false, 0.0))
                    return
                }
                
                let request = VNDetectFaceLandmarksRequest { request, error in
                    guard error == nil,
                          let results = request.results as? [VNFaceObservation] else {
                        continuation.resume(returning: (false, 0.0))
                        return
                    }
                    
                    var minEAR: Float = 1.0
                    var hasClosedEyes = false
                    
                    for faceObservation in results {
                        guard let landmarks = faceObservation.landmarks else { continue }
                        
                        // Check both eyes and get the minimum EAR
                        if let leftEye = landmarks.leftEye {
                            let leftEAR = self.calculateEyeAspectRatio(eyePoints: leftEye.normalizedPoints)
                            minEAR = min(minEAR, leftEAR)
                        }
                        
                        if let rightEye = landmarks.rightEye {
                            let rightEAR = self.calculateEyeAspectRatio(eyePoints: rightEye.normalizedPoints)
                            minEAR = min(minEAR, rightEAR)
                        }
                    }
                    
                    hasClosedEyes = minEAR < self.eyeAspectRatioThreshold
                    let confidence = hasClosedEyes ? (self.eyeAspectRatioThreshold - minEAR) / self.eyeAspectRatioThreshold : 0.0
                    
                    continuation.resume(returning: (hasClosedEyes, confidence))
                }
                
                let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
                
                do {
                    try handler.perform([request])
                } catch {
                    continuation.resume(returning: (false, 0.0))
                }
            }
        }
    }
}
