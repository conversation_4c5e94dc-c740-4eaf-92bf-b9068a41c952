// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		2DF558B42E34A5F400AAE3E9 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2DF5589E2E34A5F100AAE3E9 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2DF558A52E34A5F100AAE3E9;
			remoteInfo = PhotoCleaner;
		};
		2DF558BE2E34A5F400AAE3E9 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2DF5589E2E34A5F100AAE3E9 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2DF558A52E34A5F100AAE3E9;
			remoteInfo = PhotoCleaner;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		2DF558A62E34A5F100AAE3E9 /* PhotoCleaner.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = PhotoCleaner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		2DF558B32E34A5F400AAE3E9 /* PhotoCleanerTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PhotoCleanerTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		2DF558BD2E34A5F400AAE3E9 /* PhotoCleanerUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PhotoCleanerUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		2DF558A82E34A5F100AAE3E9 /* PhotoCleaner */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PhotoCleaner;
			sourceTree = "<group>";
		};
		2DF558B62E34A5F400AAE3E9 /* PhotoCleanerTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PhotoCleanerTests;
			sourceTree = "<group>";
		};
		2DF558C02E34A5F400AAE3E9 /* PhotoCleanerUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PhotoCleanerUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		2DF558A32E34A5F100AAE3E9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DF558B02E34A5F400AAE3E9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DF558BA2E34A5F400AAE3E9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2DF5589D2E34A5F100AAE3E9 = {
			isa = PBXGroup;
			children = (
				2DF558A82E34A5F100AAE3E9 /* PhotoCleaner */,
				2DF558B62E34A5F400AAE3E9 /* PhotoCleanerTests */,
				2DF558C02E34A5F400AAE3E9 /* PhotoCleanerUITests */,
				2DF558A72E34A5F100AAE3E9 /* Products */,
			);
			sourceTree = "<group>";
		};
		2DF558A72E34A5F100AAE3E9 /* Products */ = {
			isa = PBXGroup;
			children = (
				2DF558A62E34A5F100AAE3E9 /* PhotoCleaner.app */,
				2DF558B32E34A5F400AAE3E9 /* PhotoCleanerTests.xctest */,
				2DF558BD2E34A5F400AAE3E9 /* PhotoCleanerUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		2DF558A52E34A5F100AAE3E9 /* PhotoCleaner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2DF558C72E34A5F400AAE3E9 /* Build configuration list for PBXNativeTarget "PhotoCleaner" */;
			buildPhases = (
				2DF558A22E34A5F100AAE3E9 /* Sources */,
				2DF558A32E34A5F100AAE3E9 /* Frameworks */,
				2DF558A42E34A5F100AAE3E9 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				2DF558A82E34A5F100AAE3E9 /* PhotoCleaner */,
			);
			name = PhotoCleaner;
			packageProductDependencies = (
			);
			productName = PhotoCleaner;
			productReference = 2DF558A62E34A5F100AAE3E9 /* PhotoCleaner.app */;
			productType = "com.apple.product-type.application";
		};
		2DF558B22E34A5F400AAE3E9 /* PhotoCleanerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2DF558CA2E34A5F400AAE3E9 /* Build configuration list for PBXNativeTarget "PhotoCleanerTests" */;
			buildPhases = (
				2DF558AF2E34A5F400AAE3E9 /* Sources */,
				2DF558B02E34A5F400AAE3E9 /* Frameworks */,
				2DF558B12E34A5F400AAE3E9 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2DF558B52E34A5F400AAE3E9 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				2DF558B62E34A5F400AAE3E9 /* PhotoCleanerTests */,
			);
			name = PhotoCleanerTests;
			packageProductDependencies = (
			);
			productName = PhotoCleanerTests;
			productReference = 2DF558B32E34A5F400AAE3E9 /* PhotoCleanerTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		2DF558BC2E34A5F400AAE3E9 /* PhotoCleanerUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2DF558CD2E34A5F400AAE3E9 /* Build configuration list for PBXNativeTarget "PhotoCleanerUITests" */;
			buildPhases = (
				2DF558B92E34A5F400AAE3E9 /* Sources */,
				2DF558BA2E34A5F400AAE3E9 /* Frameworks */,
				2DF558BB2E34A5F400AAE3E9 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2DF558BF2E34A5F400AAE3E9 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				2DF558C02E34A5F400AAE3E9 /* PhotoCleanerUITests */,
			);
			name = PhotoCleanerUITests;
			packageProductDependencies = (
			);
			productName = PhotoCleanerUITests;
			productReference = 2DF558BD2E34A5F400AAE3E9 /* PhotoCleanerUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2DF5589E2E34A5F100AAE3E9 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					2DF558A52E34A5F100AAE3E9 = {
						CreatedOnToolsVersion = 16.4;
					};
					2DF558B22E34A5F400AAE3E9 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 2DF558A52E34A5F100AAE3E9;
					};
					2DF558BC2E34A5F400AAE3E9 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 2DF558A52E34A5F100AAE3E9;
					};
				};
			};
			buildConfigurationList = 2DF558A12E34A5F100AAE3E9 /* Build configuration list for PBXProject "PhotoCleaner" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 2DF5589D2E34A5F100AAE3E9;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 2DF558A72E34A5F100AAE3E9 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2DF558A52E34A5F100AAE3E9 /* PhotoCleaner */,
				2DF558B22E34A5F400AAE3E9 /* PhotoCleanerTests */,
				2DF558BC2E34A5F400AAE3E9 /* PhotoCleanerUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		2DF558A42E34A5F100AAE3E9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DF558B12E34A5F400AAE3E9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DF558BB2E34A5F400AAE3E9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		2DF558A22E34A5F100AAE3E9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DF558AF2E34A5F400AAE3E9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DF558B92E34A5F400AAE3E9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		2DF558B52E34A5F400AAE3E9 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2DF558A52E34A5F100AAE3E9 /* PhotoCleaner */;
			targetProxy = 2DF558B42E34A5F400AAE3E9 /* PBXContainerItemProxy */;
		};
		2DF558BF2E34A5F400AAE3E9 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2DF558A52E34A5F100AAE3E9 /* PhotoCleaner */;
			targetProxy = 2DF558BE2E34A5F400AAE3E9 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		2DF558C52E34A5F400AAE3E9 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = FN76ACWU4H;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		2DF558C62E34A5F400AAE3E9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = FN76ACWU4H;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		2DF558C82E34A5F400AAE3E9 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = FN76ACWU4H;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.phone.PhotoCleaner;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		2DF558C92E34A5F400AAE3E9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = FN76ACWU4H;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.phone.PhotoCleaner;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		2DF558CB2E34A5F400AAE3E9 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = FN76ACWU4H;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.phone.PhotoCleanerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/PhotoCleaner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/PhotoCleaner";
			};
			name = Debug;
		};
		2DF558CC2E34A5F400AAE3E9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = FN76ACWU4H;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.phone.PhotoCleanerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/PhotoCleaner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/PhotoCleaner";
			};
			name = Release;
		};
		2DF558CE2E34A5F400AAE3E9 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = FN76ACWU4H;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.phone.PhotoCleanerUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = PhotoCleaner;
			};
			name = Debug;
		};
		2DF558CF2E34A5F400AAE3E9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = FN76ACWU4H;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.phone.PhotoCleanerUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = PhotoCleaner;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2DF558A12E34A5F100AAE3E9 /* Build configuration list for PBXProject "PhotoCleaner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2DF558C52E34A5F400AAE3E9 /* Debug */,
				2DF558C62E34A5F400AAE3E9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2DF558C72E34A5F400AAE3E9 /* Build configuration list for PBXNativeTarget "PhotoCleaner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2DF558C82E34A5F400AAE3E9 /* Debug */,
				2DF558C92E34A5F400AAE3E9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2DF558CA2E34A5F400AAE3E9 /* Build configuration list for PBXNativeTarget "PhotoCleanerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2DF558CB2E34A5F400AAE3E9 /* Debug */,
				2DF558CC2E34A5F400AAE3E9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2DF558CD2E34A5F400AAE3E9 /* Build configuration list for PBXNativeTarget "PhotoCleanerUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2DF558CE2E34A5F400AAE3E9 /* Debug */,
				2DF558CF2E34A5F400AAE3E9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 2DF5589E2E34A5F100AAE3E9 /* Project object */;
}
