import Foundation
import Photos
import UIKit

// Data model for photos that need review
struct PhotoToReview: Identifiable, Hashable {
    let id = UUID()
    let asset: PHAsset
    let reason: ReviewReason
    let confidence: Float // 0.0 to 1.0
    var isMarkedForDeletion: Bool = false
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: PhotoToReview, rhs: PhotoToReview) -> Bool {
        lhs.id == rhs.id
    }
}

enum ReviewReason: String, CaseIterable {
    case duplicate = "Duplicate"
    case blur = "Blurry"
    case closedEyes = "Closed Eyes"
    case lowQuality = "Low Quality"
    
    var color: UIColor {
        switch self {
        case .duplicate: return .systemOrange
        case .blur: return .systemYellow
        case .closedEyes: return .systemPurple
        case .lowQuality: return .systemRed
        }
    }
    
    var icon: String {
        switch self {
        case .duplicate: return "doc.on.doc"
        case .blur: return "eye.slash"
        case .closedEyes: return "eye.slash.circle"
        case .lowQuality: return "exclamationmark.triangle"
        }
    }
}

@MainActor
class PhotoLibraryManager: ObservableObject {
    @Published var authorizationStatus: PHAuthorizationStatus = .notDetermined
    @Published var photosToReview: [PhotoToReview] = []
    @Published var allPhotos: [PHAsset] = []
    @Published var isScanning = false
    
    // Statistics
    @Published var duplicateCount = 0
    @Published var blurryCount = 0
    @Published var closedEyesCount = 0
    @Published var totalPhotosScanned = 0
    
    private let duplicateDetector = DuplicateDetector()
    private let blurDetector = BlurDetector()
    private let eyeDetector = EyeDetector()
    
    init() {
        authorizationStatus = PHPhotoLibrary.authorizationStatus(for: .readWrite)
    }
    
    func requestPhotoLibraryPermission() {
        PHPhotoLibrary.requestAuthorization(for: .readWrite) { [weak self] status in
            DispatchQueue.main.async {
                self?.authorizationStatus = status
            }
        }
    }
    
    func scanPhotoLibrary(progressCallback: @escaping (Double) -> Void) async {
        guard authorizationStatus == .authorized else { return }
        
        isScanning = true
        photosToReview.removeAll()
        duplicateCount = 0
        blurryCount = 0
        closedEyesCount = 0
        
        // Fetch all photos
        let fetchOptions = PHFetchOptions()
        fetchOptions.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]
        fetchOptions.predicate = NSPredicate(format: "mediaType == %d", PHAssetMediaType.image.rawValue)
        
        let fetchResult = PHAsset.fetchAssets(with: fetchOptions)
        let totalCount = fetchResult.count
        totalPhotosScanned = totalCount
        
        var assets: [PHAsset] = []
        fetchResult.enumerateObjects { asset, _, _ in
            assets.append(asset)
        }
        
        allPhotos = assets
        
        // Process photos in batches to avoid memory issues
        let batchSize = 50
        var processedCount = 0
        
        for i in stride(from: 0, to: assets.count, by: batchSize) {
            let endIndex = min(i + batchSize, assets.count)
            let batch = Array(assets[i..<endIndex])
            
            await processBatch(batch)
            
            processedCount += batch.count
            let progress = Double(processedCount) / Double(totalCount)
            progressCallback(progress)
        }
        
        // Find duplicates across all photos
        await findDuplicates(in: assets, progressCallback: progressCallback)
        
        isScanning = false
    }
    
    private func processBatch(_ assets: [PHAsset]) async {
        for asset in assets {
            await processAsset(asset)
        }
    }
    
    private func processAsset(_ asset: PHAsset) async {
        guard let image = await loadImage(from: asset) else { return }
        
        // Check for blur
        if await blurDetector.isBlurry(image: image) {
            let photoToReview = PhotoToReview(
                asset: asset,
                reason: .blur,
                confidence: await blurDetector.getBlurConfidence(image: image)
            )
            photosToReview.append(photoToReview)
            blurryCount += 1
        }
        
        // Check for closed eyes
        if await eyeDetector.hasClosedEyes(image: image) {
            let photoToReview = PhotoToReview(
                asset: asset,
                reason: .closedEyes,
                confidence: await eyeDetector.getClosedEyesConfidence(image: image)
            )
            photosToReview.append(photoToReview)
            closedEyesCount += 1
        }
    }
    
    private func findDuplicates(in assets: [PHAsset], progressCallback: @escaping (Double) -> Void) async {
        let duplicateGroups = await duplicateDetector.findDuplicates(in: assets) { progress in
            // Duplicate detection is part of the overall progress
            progressCallback(0.8 + (progress * 0.2))
        }
        
        for group in duplicateGroups {
            // Keep the first photo, mark others for review
            for i in 1..<group.count {
                let photoToReview = PhotoToReview(
                    asset: group[i],
                    reason: .duplicate,
                    confidence: 0.9 // High confidence for duplicates
                )
                photosToReview.append(photoToReview)
                duplicateCount += 1
            }
        }
    }
    
    private func loadImage(from asset: PHAsset) async -> UIImage? {
        return await withCheckedContinuation { continuation in
            let options = PHImageRequestOptions()
            options.isSynchronous = false
            options.deliveryMode = .highQualityFormat
            options.resizeMode = .exact
            
            let targetSize = CGSize(width: 300, height: 300) // Smaller size for processing
            
            PHImageManager.default().requestImage(
                for: asset,
                targetSize: targetSize,
                contentMode: .aspectFit,
                options: options
            ) { image, _ in
                continuation.resume(returning: image)
            }
        }
    }
    
    func deleteMarkedPhotos() async -> Bool {
        let assetsToDelete = photosToReview.filter { $0.isMarkedForDeletion }.map { $0.asset }
        
        guard !assetsToDelete.isEmpty else { return true }
        
        return await withCheckedContinuation { continuation in
            PHPhotoLibrary.shared().performChanges({
                PHAssetChangeRequest.deleteAssets(assetsToDelete as NSArray)
            }) { success, error in
                if success {
                    DispatchQueue.main.async {
                        self.photosToReview.removeAll { $0.isMarkedForDeletion }
                    }
                }
                continuation.resume(returning: success)
            }
        }
    }
    
    func markPhotoForDeletion(_ photo: PhotoToReview) {
        if let index = photosToReview.firstIndex(where: { $0.id == photo.id }) {
            photosToReview[index].isMarkedForDeletion = true
        }
    }
    
    func unmarkPhotoForDeletion(_ photo: PhotoToReview) {
        if let index = photosToReview.firstIndex(where: { $0.id == photo.id }) {
            photosToReview[index].isMarkedForDeletion = false
        }
    }
    
    func getMarkedPhotosCount() -> Int {
        return photosToReview.filter { $0.isMarkedForDeletion }.count
    }
}
